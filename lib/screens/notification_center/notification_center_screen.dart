import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/notification_model.dart';
import 'package:railops/models/notification_provider.dart';
import 'package:railops/models/notification_preferences_model.dart'
    hide NotificationType;
import 'package:railops/models/user_model.dart';
import 'package:railops/services/fcm_token_service.dart';
import 'package:railops/services/firestore_token_service.dart';
import 'package:railops/services/firebase_cloud_function_service.dart';
import 'package:railops/routes.dart';

class NotificationCenterScreen extends StatefulWidget {
  const NotificationCenterScreen({super.key});

  @override
  State<NotificationCenterScreen> createState() =>
      _NotificationCenterScreenState();
}

class _NotificationCenterScreenState extends State<NotificationCenterScreen> {
  @override
  void initState() {
    super.initState();
    // Refresh notifications when screen is opened
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<NotificationProvider>(context, listen: false)
          .refreshNotifications();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Notifications',
          style: TextStyle(color: Colors.black),
        ),
        actions: [
          IconButton(
            icon: const Icon(
              Icons.bug_report,
              color: Colors.red,
            ),
            tooltip: 'Test Notifications',
            onPressed: () {
              Navigator.of(context).pushNamed(Routes.notificationTesting);
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            tooltip: 'Notification Settings',
            onPressed: () {
              Navigator.of(context).pushNamed(Routes.notificationSettings);
            },
          ),
          IconButton(
            icon: const Icon(Icons.done_all),
            tooltip: 'Mark all as read',
            onPressed: () {
              Provider.of<NotificationProvider>(context, listen: false)
                  .markAllAsRead();
            },
          ),
          IconButton(
            icon: const Icon(Icons.delete_sweep),
            tooltip: 'Clear all notifications',
            onPressed: () {
              _showClearConfirmationDialog(context);
            },
          ),
        ],
      ),
      body: Consumer<NotificationProvider>(
        builder: (context, notificationProvider, child) {
          if (notificationProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (notificationProvider.notifications.isEmpty) {
            return const Center(
              child: Text(
                'No notifications',
                style: TextStyle(fontSize: 16),
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () => notificationProvider.refreshNotifications(),
            child: ListView.builder(
              itemCount: notificationProvider.notifications.length,
              itemBuilder: (context, index) {
                final notification = notificationProvider.notifications[index];
                return NotificationItem(
                  notification: notification,
                  onTap: () {
                    if (!notification.isRead && notification.id != null) {
                      notificationProvider.markAsRead(notification.id!);
                    }
                    _handleNotificationTap(context, notification);
                  },
                );
              },
            ),
          );
        },
      ),
    );
  }

  void _showClearConfirmationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Notifications'),
        content:
            const Text('Are you sure you want to clear all notifications?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              Provider.of<NotificationProvider>(context, listen: false)
                  .clearNotifications();
              Navigator.of(context).pop();
            },
            child: const Text('CLEAR'),
          ),
        ],
      ),
    );
  }

  void _handleNotificationTap(
      BuildContext context, NotificationModel notification) {
    // Handle navigation based on notification data
    if (notification.data.containsKey('route')) {
      final route = notification.data['route'];
      Navigator.of(context).pushNamed(route);
    }
  }
}

class NotificationItem extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback onTap;

  const NotificationItem({
    super.key,
    required this.notification,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final DateTime dateTime =
        DateTime.fromMillisecondsSinceEpoch(notification.timestamp);
    final String formattedDate =
        DateFormat('MMM d, yyyy • h:mm a').format(dateTime);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: notification.isRead ? 1 : 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: notification.isRead
            ? BorderSide.none
            : const BorderSide(color: Colors.blue, width: 1),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: notification.isRead
                          ? Colors.grey.withValues(alpha: 0.2)
                          : Colors.blue.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      Icons.notifications,
                      color: notification.isRead ? Colors.grey : Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          notification.title ?? 'Notification',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: notification.isRead
                                ? FontWeight.normal
                                : FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        _buildNotificationBody(notification),
                        // Add coach table format for CA/CS/EHK notifications
                        if (_hasCoachData(notification)) ...[
                          const SizedBox(height: 8),
                          _buildCoachTable(notification),
                        ],
                        const SizedBox(height: 8),
                        Text(
                          formattedDate,
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  

  /// Build notification body with enhanced formatting
  Widget _buildNotificationBody(NotificationModel notification) {
    final body = notification.body ?? '';

    // Check if this is an enhanced train location notification with table format
    if (_isEnhancedTrainNotification(notification) &&
        _containsTableFormat(body)) {
      return _buildFormattedTable(body);
    }

    // Regular text display for other notifications
    return Text(
      body,
      style: const TextStyle(
        color: Colors.black87,
        fontSize: 14,
      ),
    );
  }

  /// Check if notification is an enhanced train location update
  bool _isEnhancedTrainNotification(NotificationModel notification) {
    return notification.data.containsKey('type') &&
        notification.data['type'] == 'enhanced_train_location_update';
  }

  /// Check if body contains table format (Station | Coach | Board | Deboard | Vacant)
  bool _containsTableFormat(String body) {
    return body.contains('Station | Coach | Board') ||
        body.contains('Station|Coach|Board') ||
        body.contains('--------|-------|-------');
  }

  /// Build formatted table from notification body
  Widget _buildFormattedTable(String body) {
    try {
      final lines = body.split('\n');
      final tableLines = <String>[];
      bool inTable = false;

      // Extract table lines
      for (final line in lines) {
        if (line.contains('Station | Coach | Board') ||
            line.contains('Station|Coach|Board')) {
          inTable = true;
          tableLines.add(line);
        } else if (inTable && (line.contains('|') || line.contains('-'))) {
          tableLines.add(line);
        } else if (inTable && line.trim().isEmpty) {
          break; // End of table
        } else if (!inTable) {
          // Add non-table content before the table
          if (line.trim().isNotEmpty && !line.contains('-----')) {
            tableLines.insert(0, line);
          }
        }
      }

      if (tableLines.length >= 3) {
        // Header + separator + at least one data row
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Show any text before the table
            if (tableLines.first.contains('Train update') ||
                !tableLines.first.contains('|'))
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Text(
                  tableLines.first,
                  style: const TextStyle(
                    color: Colors.black87,
                    fontSize: 14,
                  ),
                ),
              ),
            // Build the table
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Table(
                border: TableBorder.all(color: Colors.grey.shade300),
                columnWidths: const {
                  0: FlexColumnWidth(2), // Station
                  1: FlexColumnWidth(1.5), // Coach
                  2: FlexColumnWidth(1), // Board
                  3: FlexColumnWidth(1), // Deboard
                  4: FlexColumnWidth(1), // Vacant
                },
                children: _buildTableRows(tableLines),
              ),
            ),
          ],
        );
      }
    } catch (e) {
      // If table parsing fails, fall back to regular text
      debugPrint('Error parsing notification table: $e');
    }

    // Fallback to regular text display
    return Text(
      body,
      style: const TextStyle(
        color: Colors.black87,
        fontSize: 14,
      ),
    );
  }

  /// Build table rows from table lines
  List<TableRow> _buildTableRows(List<String> tableLines) {
    final rows = <TableRow>[];

    for (int i = 0; i < tableLines.length; i++) {
      final line = tableLines[i];

      // Skip separator lines
      if (line.contains('-----')) continue;

      // Skip non-table lines
      if (!line.contains('|')) continue;

      final cells = line.split('|').map((cell) => cell.trim()).toList();

      if (cells.length >= 5) {
        final isHeader = line.contains('Station') && line.contains('Coach');

        rows.add(
          TableRow(
            decoration:
                isHeader ? BoxDecoration(color: Colors.blue.shade50) : null,
            children: cells
                .take(5)
                .map(
                  (cell) => Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      cell,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight:
                            isHeader ? FontWeight.bold : FontWeight.normal,
                        color: isHeader ? Colors.blue.shade800 : Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                )
                .toList(),
          ),
        );
      }
    }

    return rows;
  }

  /// Check if notification contains coach-specific data
  bool _hasCoachData(NotificationModel notification) {
    final data = notification.data;
    return data.containsKey('coach_data') ||
        data.containsKey('stations_notified') ||
        data.containsKey('coach_table') ||
        (notification.type == NotificationType.coachSpecific) ||
        (notification.type == NotificationType.enhancedTrainLocationUpdate);
  }

  /// Build coach table widget for CA/CS/EHK notifications
  Widget _buildCoachTable(NotificationModel notification) {
    final data = notification.data;

    // Try to extract coach data from different possible formats
    List<Map<String, dynamic>> coachTableData = [];

    if (data.containsKey('coach_table')) {
      coachTableData =
          List<Map<String, dynamic>>.from(data['coach_table'] ?? []);
    } else if (data.containsKey('stations_notified')) {
      // Extract from stations_notified format (from Firebase Cloud Function)
      final stationsNotified = data['stations_notified'];
      if (stationsNotified is List) {
        for (var station in stationsNotified) {
          if (station is Map && station.containsKey('coaches')) {
            final stationCode = station['station_code'] ?? 'Unknown';
            final coaches = station['coaches'] ?? [];
            for (var coach in coaches) {
              if (coach is Map) {
                coachTableData.add({
                  'station_code': stationCode,
                  'coach': coach['coach_number'] ?? 'Unknown',
                  'onboarding': coach['onboarding_count'] ?? 0,
                  'deboarding': coach['deboarding_count'] ?? 0,
                  'vacant': coach['vacant_count'] ?? 0,
                });
              }
            }
          }
        }
      }
    } else if (data.containsKey('coach_data')) {
      // Extract from coach_data format
      final coachData = data['coach_data'];
      if (coachData is Map) {
        coachData.forEach((coach, counts) {
          if (counts is Map) {
            coachTableData.add({
              'station_code': data['station_code'] ?? 'Unknown',
              'coach': coach,
              'onboarding': counts['onboarding'] ?? 0,
              'deboarding': counts['deboarding'] ?? 0,
              'vacant': counts['vacant'] ?? 0,
            });
          }
        });
      }
    }

    if (coachTableData.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Text(
              'Coach Details',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: Colors.blue,
              ),
            ),
          ),
          // Table header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Row(
              children: [
                Expanded(flex: 2, child: _buildTableHeader('Station')),
                Expanded(flex: 2, child: _buildTableHeader('Coach')),
                Expanded(flex: 2, child: _buildTableHeader('Onboard')),
                Expanded(flex: 2, child: _buildTableHeader('Deboard')),
                Expanded(flex: 2, child: _buildTableHeader('Vacant')),
              ],
            ),
          ),
          const Divider(height: 1),
          // Table rows
          ...coachTableData.map((row) => _buildTableRow(row)),
        ],
      ),
    );
  }

  Widget _buildTableHeader(String text) {
    return Text(
      text,
      style: const TextStyle(
        fontSize: 10,
        fontWeight: FontWeight.bold,
        color: Colors.grey,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildTableRow(Map<String, dynamic> row) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              row['station_code']?.toString() ?? '',
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              row['coach']?.toString() ?? '',
              style: const TextStyle(fontSize: 10, fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.green.shade100,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                row['onboarding']?.toString() ?? '0',
                style: TextStyle(fontSize: 10, color: Colors.green.shade700),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.orange.shade100,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                row['deboarding']?.toString() ?? '0',
                style: TextStyle(fontSize: 10, color: Colors.orange.shade700),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                row['vacant']?.toString() ?? '0',
                style: TextStyle(fontSize: 10, color: Colors.grey.shade700),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
