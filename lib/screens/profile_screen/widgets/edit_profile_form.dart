import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:provider/provider.dart';
import 'package:railops/routes.dart';
import 'package:railops/models/index.dart';
import 'package:railops/screens/profile_screen/widgets/change_email_modal.dart';
import 'package:railops/screens/user_screen/auth_provider.dart';
import 'package:railops/services/otp_services/sign_up_otp.dart';
import 'package:railops/services/profile_services/profile_services.dart';
import 'package:railops/types/profile_types/request_profile.dart';
import 'package:railops/services/authentication_services/auth_service.dart';
import 'package:railops/widgets/call_widget.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'toggle_switch_widget.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:railops/services/train_services/train_details_services.dart';
import 'package:railops/services/profile_services/profile_train_services.dart';
import 'package:railops/screens/user_screen/widgets/login_page/privacy_policies.dart';
import 'package:url_launcher/url_launcher.dart';

class EditProfileForm extends StatefulWidget {
  final UserModel userModel;
  const EditProfileForm({super.key, required this.userModel});

  @override
  _EditProfileFormState createState() => _EditProfileFormState();
}

class _EditProfileFormState extends State<EditProfileForm> {
  late UserModel userModel;
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _firstNameController;
  late TextEditingController _middleNameController;
  late TextEditingController _lastNameController;
  late TextEditingController _roleController;
  late TextEditingController _depotController;
  late TextEditingController _phoneNumberController;
  late TextEditingController _whatsappNumberController;
  late TextEditingController _emailController;
  late TextEditingController _empNumberController;
  late TextEditingController _secondaryPhoneController;

  bool _isLoadingProfile = false;
  bool _isUpdatingProfile = false;
  bool _isLoadingChangeEmail = false;
  bool _isLoadingChangePassword = false;
  bool _isLoadingChangeMobile = false;
  bool _isLoadingChangeWhatsapp = false;
  bool _isLoadingAddTrains = false;
  bool _isLogOuting = false;
  bool _showErrorModal = false;
  String _errorMessage = '';
  List<String> _trainNumbers = [];
  String? _selectedTrainNumber;
  bool _insideTrain = false;
  bool _needAlarm = false;
  String? insideTrainDate;
  String? userType;

  @override
  void initState() {
    super.initState();
    userModel = Provider.of<UserModel>(context, listen: false);
    userType = userModel.userType;
    _firstNameController = TextEditingController();
    _middleNameController = TextEditingController();
    _lastNameController = TextEditingController();
    _roleController = TextEditingController();
    _depotController = TextEditingController();
    _phoneNumberController = TextEditingController();
    _whatsappNumberController = TextEditingController();
    _emailController = TextEditingController();
    _empNumberController = TextEditingController();
    _secondaryPhoneController = TextEditingController();
    _getProfile();
    _fetchTrainNumbers();
    _fetchToggleSwitchStatuses();
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _middleNameController.dispose();
    _lastNameController.dispose();
    _roleController.dispose();
    _depotController.dispose();
    _phoneNumberController.dispose();
    _whatsappNumberController.dispose();
    _emailController.dispose();
    _empNumberController.dispose();
    _secondaryPhoneController.dispose();
    super.dispose();
  }

  Future<void> _fetchTrainNumbers() async {
    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;

      final response = await TrainService.getTrainDetails(token);
      _trainNumbers = response.trainDetails.values
          .map((detail) => detail.trainNumber)
          .toList();

      setState(() {});
    } catch (e) {
      // Handle the error appropriately
      print('Error fetching train numbers: $e');
    }
  }

  void _onTrainNumberChanged(String? trainNumber) async {
    setState(() {
      _selectedTrainNumber = trainNumber;
    });
  }

  Future<void> _fetchToggleSwitchStatuses() async {
    try {
      final token = userModel.token;
      final insideTrainStatus =
          await ProfileTrainServices.getInsideTrainStatus(token);
      final needAlarmStatus =
          await ProfileTrainServices.getNeedAlarmStatus(token);

      setState(() {
        _insideTrain = insideTrainStatus['inside_train'];
        _selectedTrainNumber = insideTrainStatus['inside_train_number'];
        insideTrainDate = insideTrainStatus['inside_train_date'];
        _needAlarm = needAlarmStatus;
      });
    } catch (e) {
      print('Error fetching toggle switch statuses: $e');
    }
  }

  void _onInsideTrainToggle(int index) async {
    setState(() {
      _insideTrain = index == 0;
    });
    try {
      final token = userModel.token;
      await ProfileTrainServices.toggleInsideTrain(
        token,
        _insideTrain,
        _selectedTrainNumber ?? '',
      );
    } catch (e) {
      print('Error toggling inside train status: $e');
    }
  }

  void _onNeedAlarmToggle(int index) async {
    setState(() {
      _needAlarm = index == 0;
    });
    try {
      final token = userModel.token;
      await ProfileTrainServices.toggleNeedAlarm(token, _needAlarm);
    } catch (e) {
      print('Error toggling need alarm status: $e');
    }
  }

  Future<void> _getProfile() async {
    setState(() {
      _isLoadingProfile = true;
    });
    try {
      final token = userModel.token;
      final profileResponse = await ProfileService.getProfile(token);
      setState(() {
        _firstNameController.text = profileResponse.user?.firstName ?? '';
        _middleNameController.text = profileResponse.user?.middleName ?? '';
        _lastNameController.text = profileResponse.user?.lastName ?? '';
        _roleController.text = profileResponse.role ?? '';
        _depotController.text = profileResponse.user?.depo ?? '';
        _phoneNumberController.text = profileResponse.user?.phone ?? '';
        _whatsappNumberController.text =
            profileResponse.user?.whatsappNumber ?? '';
        _emailController.text = profileResponse.user?.email ?? '';
        _empNumberController.text = profileResponse.user?.empNumber ?? '';
        _secondaryPhoneController.text =
            profileResponse.user?.secondaryPhone ?? '';
      });
    } catch (e) {
      setState(() {
        _showErrorModal = true;
        _errorMessage = '$e';
      });
    } finally {
      setState(() {
        _isLoadingProfile = false;
      });
    }
  }

  Future<void> _updateProfile() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      setState(() {
        _isUpdatingProfile = true;
      });
      try {
        final token = userModel.token;
        final updatedProfile = RequestProfile(
          firstName: _firstNameController.text,
          middleName: _middleNameController.text,
          lastName: _lastNameController.text,
          empNumber: _empNumberController.text,
          secondaryPhone: _secondaryPhoneController.text,
        );
        final message =
            await ProfileService.updateProfile(token, updatedProfile);
        print(message);
        setState(() {
          _showErrorModal = true;
          _errorMessage = message;
        });

        _showSuccessDialog(message);
      } catch (e) {
        setState(() {
          _showErrorModal = true;
          _errorMessage = '$e';
        });
        _showSuccessDialog('$e');
      } finally {
        setState(() {
          _isUpdatingProfile = false;
        });
      }
    } else {
      print("not valid");
    }
  }

  Future<void> _changeEmail() async {
    setState(() {
      _isLoadingChangeEmail = true;
    });
    try {
      await Future.delayed(const Duration(seconds: 1)); // Simulate delay
      Navigator.pushNamed(context, '/change_mail');
    } catch (e) {
      setState(() {
        _showErrorModal = true;
        _errorMessage = '$e';
      });
    } finally {
      setState(() {
        _isLoadingChangeEmail = false;
      });
    }
  }

  Future<void> _changePassword() async {
    setState(() {
      _isLoadingChangePassword = true;
    });
    try {
      await Future.delayed(const Duration(seconds: 1));
      Navigator.pushNamed(context, '/change_password');
    } catch (e) {
      setState(() {
        _showErrorModal = true;
        _errorMessage = '$e';
      });
    } finally {
      setState(() {
        _isLoadingChangePassword = false;
      });
    }
  }

  Future<void> _changeMobile() async {
    setState(() {
      _isLoadingChangeMobile = true;
    });
    try {
      await Future.delayed(const Duration(seconds: 1)); // Simulate delay
      Navigator.pushNamed(context, '/change_mobile');
    } catch (e) {
      setState(() {
        _showErrorModal = true;
        _errorMessage = '$e';
      });
    } finally {
      setState(() {
        _isLoadingChangeMobile = false;
      });
    }
  }

  Future<void> _changeWhatsapp() async {
    setState(() {
      _isLoadingChangeWhatsapp = true;
    });
    try {
      await Future.delayed(const Duration(seconds: 1)); // Simulate delay
      Navigator.pushNamed(context, '/change_whatsapp');
    } catch (e) {
      setState(() {
        _showErrorModal = true;
        _errorMessage = '$e';
      });
    } finally {
      setState(() {
        _isLoadingChangeWhatsapp = false;
      });
    }
  }

  Future<void> _AddTrains() async {
    setState(() {
      _isLoadingAddTrains = true;
    });
    try {
      await Future.delayed(const Duration(seconds: 1)); // Simulate delay
      Navigator.pushNamed(context, '/add-train-profile');
    } catch (e) {
      setState(() {
        _showErrorModal = true;
        _errorMessage = '$e';
      });
    } finally {
      setState(() {
        _isLoadingAddTrains = false;
      });
    }
  }

  Future<void> _showChangeEmailModalOnce(String email) async {
    //print('Email: $email');
    if (email.startsWith("noemail")) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showDialog(
          context: context,
          builder: (BuildContext context) => ChangeEmailModal(),
        );
      });
    }
  }

  Future<void> _deactivateAccount() async {
    final token = userModel.token;
    final profileResponse = await ProfileService.getProfile(token);
    final email = profileResponse.user?.email ?? '';
    final phone = profileResponse.user?.phone ?? '';

    final bool isNoEmail = email.toLowerCase().startsWith("noemail");

    final bool confirmDeactivation = await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirm Deactivation'),
          content: Text(isNoEmail
              ? 'You need valid email before deactivation.'
              : 'We will send OTPs to your email and phone for verification.'),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            if (!isNoEmail)
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Proceed'),
              ),
            if (isNoEmail)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(false);
                  if (isNoEmail) _showChangeEmailModalOnce(email);
                  _getProfile();
                },
                child: const Text('Add Email'),
              ),
          ],
        );
      },
    );

    if (confirmDeactivation && !isNoEmail) {
      await _getProfile();
      _sendDeactivationOTP();
    }
  }

  Future<void> _sendDeactivationOTP() async {
    setState(() => _isUpdatingProfile = true);
    final token = userModel.token;
    final profileResponse = await ProfileService.getProfile(token);
    final email = profileResponse.user?.email ?? '';

    try {
      final token = userModel.token;
      // Send both OTPs simultaneously
      await Future.wait([
        AuthService.sendDeactivationOTP(token, email),
        SignUpOtp.sendOtp(_phoneNumberController.text, "deactivate_account")
      ]);

      setState(() => _isUpdatingProfile = false);
      _showVerificationFlow();
    } catch (e) {
      setState(() => _isUpdatingProfile = false);
      _showSuccessDialog('Failed to send OTPs: $e');
    }
  }

  void _showVerificationFlow() {
    final emailOtpController = TextEditingController();
    final phoneOtpController = TextEditingController();
    int currentStep = 0;
    bool isVerifying = false;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          child: Material(
            borderRadius: BorderRadius.circular(16),
            child: StatefulBuilder(
              builder: (context, setState) {
                return Stepper(
                  currentStep: currentStep,
                  controlsBuilder: (context, details) {
                    if (currentStep == 0) {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          TextButton(
                            onPressed: isVerifying
                                ? null
                                : () async {
                                    if (emailOtpController.text.isEmpty) return;

                                    setState(() => isVerifying = true);
                                    try {
                                      await AuthService.verifyDeactivationOTP(
                                          userModel.token, {
                                        'otp': emailOtpController.text,
                                        "email": _emailController.text
                                      });
                                      setState(() => currentStep = 1);
                                    } catch (e) {
                                      _showSuccessDialog('Email OTP Error: $e');
                                    }
                                    setState(() => isVerifying = false);
                                  },
                            child: const Text('Verify Email'),
                          ),
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('Cancel'),
                          ),
                        ],
                      );
                    }
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        TextButton(
                          style: TextButton.styleFrom(
                            backgroundColor: Colors.red,
                          ),
                          onPressed: isVerifying
                              ? null
                              : () async {
                                  if (phoneOtpController.text.isEmpty) return;

                                  setState(() => isVerifying = true);
                                  try {
                                    await AuthService.loginByMobile(
                                        phoneOtpController.text,
                                        _phoneNumberController.text);

                                    // Both OTPs verified - deactivate
                                    final message =
                                        await AuthService.deactivateAccount(
                                            userModel.token);
                                    Navigator.pop(context);
                                    _showSuccessDialog(message);

                                    Provider.of<AuthModel>(context,
                                            listen: false)
                                        .logout();
                                    Navigator.pushReplacementNamed(
                                        context, Routes.login);
                                  } catch (e) {
                                    _showSuccessDialog('Phone OTP Error: $e');
                                  }
                                  setState(() => isVerifying = false);
                                },
                          child: const Text(
                            'Deactivate',
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                        TextButton(
                          style: TextButton.styleFrom(
                            backgroundColor: Colors.green,
                          ),
                          onPressed: () => Navigator.pop(context),
                          child: const Text('Back',
                              style: TextStyle(color: Colors.white)),
                        ),
                      ],
                    );
                  },
                  steps: [
                    Step(
                      title: const Text('Email Verification'),
                      content: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: TextField(
                          controller: emailOtpController,
                          decoration: const InputDecoration(
                            labelText: 'Enter Email OTP',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ),
                    Step(
                      title: const Text('Phone Verification'),
                      content: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: TextField(
                          controller: phoneOtpController,
                          decoration: const InputDecoration(
                            labelText: 'Enter Phone OTP',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        );
      },
    );
  }

  void _launchPhoneDialer(String phoneNumber) async {
    const countryCode = '+91';
    final formattedNumber = '$countryCode$phoneNumber';
    final url = 'tel:$formattedNumber';
    print('Dialing $formattedNumber');
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not open the dialer';
    }
  }

  Future<void> _handleLogout() async {
    final bool confirmLogout = await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text("Logout Confirmation"),
          content: const Text("Do you want to logout now?"),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(false);
              },
              style: TextButton.styleFrom(
                backgroundColor: Colors.green,
              ),
              child: const Text(
                "No",
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop(true);
              },
              style: TextButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: const Text(
                "Yes",
                style: TextStyle(color: Colors.white),
              ),
            )
          ],
        );
      },
    );

    if (confirmLogout) {
      setState(() {
        _isLogOuting = true;
      });

      final authModel = Provider.of<AuthModel>(context, listen: false);
      final prefs = await SharedPreferences.getInstance();

      final isFingerprintEnabled = prefs.getBool('use_fingerprint') ?? false;

      try {
        // Optional: Call backend logout
        await AuthService.logout(
          authModel.loginResponse!.refreshToken,
          authModel.loginResponse!.token,
        );
      } catch (e) {
        // handle error or log
      } finally {
        await prefs.remove('lastChangeEmailModalTime');

        if (!isFingerprintEnabled) {
          // 🔴 If fingerprint not enabled, clear all login data
          await prefs.remove('loginResponse');
          await prefs.remove('user_name');
          await prefs.remove('user_email');
          await prefs.setBool('is_logged_in', false);
        }

        // ✅ Clear auth state in app
        authModel.logout();

        // ✅ Navigate to login screen
        Navigator.pushReplacementNamed(context, Routes.login);
      }
    }
  }

  Future<void> _resetForm() async {
    // Clear all form fields
    _firstNameController.clear();
    _middleNameController.clear();
    _lastNameController.clear();
    _roleController.clear();
    _depotController.clear();
    _phoneNumberController.clear();
    _whatsappNumberController.clear();
    _emailController.clear();
    _empNumberController.clear();
    _secondaryPhoneController.clear();

    // Reset state variables
    setState(() {
      _selectedTrainNumber = null;
      _insideTrain = false;
      _needAlarm = false;
    });

    // Re-fetch profile and train numbers to reset the state
    await _getProfile();
    await _fetchTrainNumbers();
    await _fetchToggleSwitchStatuses();
  }

  Widget _buildProfileForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          _buildTextField(
            'First Name',
            controller: _firstNameController,
            isEditable: true,
            isPhoneNumber: false,
          ),
          _buildTextField(
            'Middle Name',
            controller: _middleNameController,
            isEditable: true,
            isPhoneNumber: false,
            isRequired: false,
          ),
          _buildTextField(
            'Last Name',
            controller: _lastNameController,
            isEditable: true,
            isPhoneNumber: false,
          ),
          _buildTextField(
            'Employee Number',
            controller: _empNumberController,
            isEditable: true,
            isPhoneNumber: false,
          ),
          _buildTextField(
            'Role',
            controller: _roleController,
            isEditable: false,
            isPhoneNumber: false,
          ),
          _buildTextField(
            'Depot',
            controller: _depotController,
            isEditable: false,
            isPhoneNumber: false,
          ),
          _buildTextField(
            'Phone Number',
            controller: _phoneNumberController,
            isEditable: false,
            isPhoneNumber: true,
          ),
          _buildTextField('Whatsapp Number',
              controller: _whatsappNumberController,
              isEditable: false,
              isWhatsappNumber: true,
              isRequired: false),
          _buildTextField(
            'Secondary Phone Number',
            controller: _secondaryPhoneController,
            isEditable: true,
            isPhoneNumber: true,
            isRequired: false,
          ),
          _buildTextField(
            'Email',
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            isEditable: false,
            isPhoneNumber: false,
          ),
        ],
      ),
    );
  }

  Widget _buildTextField(
    String label, {
    required TextEditingController controller,
    TextInputType keyboardType = TextInputType.text,
    bool isEditable = true,
    bool isPhoneNumber = false,
    bool isWhatsappNumber = false,
    bool isRequired = true,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextFormField(
        controller: controller,
        readOnly: !isEditable,
        style: TextStyle(
          color: isEditable ? Colors.black : Colors.grey,
        ),
        decoration: InputDecoration(
          labelText: label,
          border: OutlineInputBorder(
            borderSide: BorderSide(
              color: isEditable ? Colors.blue : Colors.grey[600]!,
            ),
          ),
          filled: !isEditable,
          fillColor: Colors.grey[200],
          suffixIcon: isPhoneNumber
              ? IconButton(
                  icon: Icon(
                    Icons.phone,
                    // Make the icon grey when the field is not editable
                    color: !isEditable ? Colors.grey[400] : null,
                  ),
                  // Disable the button if the field is not editable
                  onPressed: !isEditable
                      ? null // Always disable the call button when field is read-only
                      : () {
                          // Only allow calling if the field is editable
                          showCallModal(context, controller.text);
                        },
                )
              : null,
        ),
        keyboardType: keyboardType,
        inputFormatters: isPhoneNumber
            ? [
                LengthLimitingTextInputFormatter(10),
                FilteringTextInputFormatter.digitsOnly,
              ]
            : null,
        validator: (value) {
          // Check if field is required and empty
          if (isRequired && (value == null || value.isEmpty)) {
            return 'Please enter $label';
          }

          // For phone number validation, only validate if there's actually a value
          if (isPhoneNumber && value != null && value.isNotEmpty) {
            if (value.length != 10) {
              return 'Phone number must be 10 digits';
            }
          }

          // Special validation for secondary phone number
          // This only runs if secondary phone number has a value (not empty)
          if (label == 'Secondary Phone Number' &&
              value != null &&
              value.isNotEmpty) {
            String primaryPhone = _phoneNumberController.text.trim();
            String secondaryPhone = value.trim();

            if (primaryPhone == secondaryPhone) {
              return 'Phone number and Secondary phone number must be different.';
            }
          }

          return null; // No error - validation passed
        },
      ),
    );
  }

  Widget _buildButton(String label, bool isLoading, VoidCallback onPressed,
      {Color? buttonColor, Color? textColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor:
              buttonColor ?? const Color.fromARGB(255, 223, 224, 236),
          foregroundColor: textColor ?? Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          side: const BorderSide(color: Colors.black, width: 0.5),
          minimumSize: const Size(double.infinity, 50),
        ),
        child: isLoading
            ? const CircularProgressIndicator(color: Colors.white)
            : Text(label),
      ),
    );
  }

  Widget _buildButtonsColumn(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        _buildButton('Change Email', _isLoadingChangeEmail, _changeEmail),
        _buildButton(
            'Change Password', _isLoadingChangePassword, _changePassword),
        _buildButton(
            'Change Mobile Number', _isLoadingChangeMobile, _changeMobile),
        _buildButton('Change Whatsapp Number', _isLoadingChangeWhatsapp,
            _changeWhatsapp),
        Consumer<AuthProvider>(
          builder: (context, fp, _) {
            return Container(
              margin: const EdgeInsets.symmetric(vertical: 12),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade400),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Expanded(
                    child: Text(
                      'Enable Fingerprint Login',
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                    ),
                  ),
                  Switch.adaptive(
                    value: fp.isEnabled,
                    onChanged: (val) => fp.toggle(val),
                    activeColor: Colors.indigo,
                  ),
                ],
              ),
            );
          },
        ),

        const SizedBox(height: 16.0), // Space before Deactivate Account button
        _buildButton(
          'Deactivate Account', _isUpdatingProfile, _deactivateAccount,
          buttonColor: const Color.fromARGB(255, 186, 44, 34),
          textColor: Colors.white, // Set text color to white
        ),
        _buildButton(
          'Logout', _isLogOuting, _handleLogout,
          buttonColor: const Color.fromARGB(255, 186, 44, 34),
          textColor: Colors.white, // Set text color to white
        ),
        privacyPolicyRender(context),
      ],
    );
  }

  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Success'),
          content: Text(message),
          actions: [
            TextButton(
              child: const Text('OK'),
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    double padding = 16.0;
    double screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _resetForm, // Set the onRefresh callback to reset the form
        child: Stack(
          children: [
            SingleChildScrollView(
              physics:
                  const AlwaysScrollableScrollPhysics(), // Ensure scrollable content
              padding: EdgeInsets.all(padding),
              child: screenWidth > 600
                  ? Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Expanded(
                          child: Card(
                            color: Colors.white,
                            child: Padding(
                              padding: EdgeInsets.all(padding),
                              child: _isLoadingProfile
                                  ? const Center(
                                      child: CircularProgressIndicator())
                                  : Column(
                                      children: [
                                        _buildProfileForm(),
                                        SizedBox(height: padding),
                                        _buildButton(
                                          'Update Profile',
                                          _isUpdatingProfile,
                                          _updateProfile,
                                        ),
                                      ],
                                    ),
                            ),
                          ),
                        ),
                        SizedBox(width: padding),
                        Expanded(
                          child: Column(
                            children: [
                              Card(
                                color: Colors.white,
                                child: Padding(
                                  padding: EdgeInsets.all(padding),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      DropdownSearch<String>(
                                        items: _trainNumbers,
                                        selectedItem: _selectedTrainNumber,
                                        onChanged: (value) {
                                          _onTrainNumberChanged(value);
                                        },
                                        dropdownDecoratorProps:
                                            const DropDownDecoratorProps(
                                          dropdownSearchDecoration:
                                              InputDecoration(
                                            labelText: 'Select Train Number',
                                            border: OutlineInputBorder(),
                                          ),
                                        ),
                                        popupProps: PopupProps.menu(
                                          showSearchBox: true,
                                          itemBuilder:
                                              (context, item, isSelected) {
                                            return ListTile(
                                              title: Text(item),
                                              selected: isSelected,
                                            );
                                          },
                                        ),
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Please select a train number';
                                          }
                                          return null;
                                        },
                                      ),
                                      const SizedBox(height: 10),
                                      Column(
                                        children: [
                                          const Text(
                                            "Inside Train",
                                            style: TextStyle(
                                              fontSize: 18.0,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(width: 10),
                                          ToggleSwitchWidget(
                                            onToggleCallback:
                                                _onInsideTrainToggle,
                                            enabled:
                                                _selectedTrainNumber != null &&
                                                    _selectedTrainNumber!
                                                        .isNotEmpty,
                                            initialIndex: _insideTrain ? 0 : 1,
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 10),
                                      Column(
                                        children: [
                                          const Text(
                                            "Need Alarm",
                                            style: TextStyle(
                                              fontSize: 18.0,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(width: 10),
                                          ToggleSwitchWidget(
                                            onToggleCallback:
                                                _onNeedAlarmToggle,
                                            initialIndex: _needAlarm ? 0 : 1,
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 10),
                                      _buildButton('Add Trains',
                                          _isLoadingAddTrains, _AddTrains),
                                      SizedBox(height: padding),
                                    ],
                                  ),
                                ),
                              ),
                              Card(
                                color: Colors.white,
                                child: Padding(
                                  padding: EdgeInsets.all(padding),
                                  child: Column(
                                    children: [
                                      _buildButtonsColumn(context),
                                      SizedBox(height: padding),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
                  : Column(
                      children: <Widget>[
                        Card(
                          color: Colors.white,
                          child: Padding(
                            padding: EdgeInsets.all(padding),
                            child: _isLoadingProfile
                                ? const Center(
                                    child: CircularProgressIndicator())
                                : Column(
                                    children: [
                                      _buildProfileForm(),
                                      SizedBox(height: padding),
                                      _buildButton(
                                        'Update Profile',
                                        _isUpdatingProfile,
                                        _updateProfile,
                                      ),
                                    ],
                                  ),
                          ),
                        ),
                        SizedBox(height: padding),
                        Card(
                          color: Colors.white,
                          child: Padding(
                            padding: EdgeInsets.all(padding),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                DropdownSearch<String>(
                                  items: _trainNumbers,
                                  selectedItem: _selectedTrainNumber,
                                  onChanged: (value) {
                                    _onTrainNumberChanged(value);
                                  },
                                  dropdownDecoratorProps:
                                      const DropDownDecoratorProps(
                                    dropdownSearchDecoration: InputDecoration(
                                      labelText: 'Select Train Number',
                                      border: OutlineInputBorder(),
                                    ),
                                  ),
                                  popupProps: PopupProps.menu(
                                    showSearchBox: true,
                                    itemBuilder: (context, item, isSelected) {
                                      return ListTile(
                                        title: Text(item),
                                        selected: isSelected,
                                      );
                                    },
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please select a train number';
                                    }
                                    return null;
                                  },
                                  enabled: !["EHK", "coach attendent", "OBHS"]
                                      .contains(userType),
                                ),
                                const SizedBox(height: 10),
                                TextFormField(
                                  decoration: const InputDecoration(
                                    labelText: 'Select Date',
                                    border: OutlineInputBorder(),
                                  ),
                                  style: const TextStyle(color: Colors.black),
                                  enabled: false,
                                  controller: TextEditingController(
                                      text: insideTrainDate),
                                ),
                                const SizedBox(height: 10),
                                Column(
                                  children: [
                                    const Text(
                                      "Inside Train",
                                      style: TextStyle(
                                        fontSize: 18.0,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    ToggleSwitchWidget(
                                      onToggleCallback: _onInsideTrainToggle,
                                      enabled: _selectedTrainNumber != null &&
                                          _selectedTrainNumber!.isNotEmpty &&
                                          !["EHK", "coach attendent", "OBHS"]
                                              .contains(userType),
                                      initialIndex: _insideTrain ? 0 : 1,
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 10),
                                Column(
                                  children: [
                                    const Text(
                                      "Need Alarm",
                                      style: TextStyle(
                                        fontSize: 18.0,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    ToggleSwitchWidget(
                                      onToggleCallback: _onNeedAlarmToggle,
                                      initialIndex: _needAlarm ? 0 : 1,
                                      // enabled: !["EHK", "coach attendent", "OBHS"].contains(userType),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 10),
                                !["EHK", "coach attendent", "OBHS"]
                                        .contains(userType)
                                    ? _buildButton('Add Trains',
                                        _isLoadingAddTrains, _AddTrains)
                                    : const SizedBox(height: 0),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: padding),
                        Card(
                          color: Colors.white,
                          child: Padding(
                            padding: EdgeInsets.all(padding),
                            child: Column(
                              children: [
                                _buildButtonsColumn(context),
                                SizedBox(height: padding),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
