import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:latlong2/latlong.dart';
import 'package:provider/provider.dart';
import 'package:railops/core/comman_widgets/show_info_modal.dart';
import 'package:railops/main.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/screens/attendance/attendance_details.dart';
import 'package:railops/screens/attendance/image_upload.dart';
import 'package:railops/screens/profile_screen/widgets/change_email_modal.dart';
import 'package:railops/services/assign_ehk_ca_services/return_gap_services.dart';
import 'package:railops/services/attendance_services/attendance_sevices.dart';
import 'package:railops/services/attendance_services/location_service.dart';
import 'package:railops/services/edit_train_services/edit_train_services.dart';
import 'package:railops/services/location_services/location_services.dart';
import 'package:railops/services/profile_services/profile_services.dart';
import 'package:railops/services/profile_services/profile_train_services.dart';
import 'package:railops/types/assign_ehk_ca_types/return_gap_types.dart';
import 'package:railops/types/attendance_types/onboarding_response.dart';
import 'package:railops/types/edit_train_types/edit_train_types.dart'
    hide EditTrainServices;
import 'package:railops/types/train_types/train_charting_response.dart';
import 'package:railops/utils/permission_handler_service.dart';
import 'package:railops/widgets/index.dart';
import 'package:railops/services/train_services/index.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:railops/types/attendance_types/passchart_detail_types.dart';
import 'package:railops/services/attendance_services/passenger_details_service.dart';
import 'package:permission_handler/permission_handler.dart';

class AttendanceScreen extends StatefulWidget {
  @override
  _AttendanceScreenState createState() => _AttendanceScreenState();
}

class _AttendanceScreenState extends State<AttendanceScreen> {
  String? selectedTrain;
  List<String> stations = [];
  String startTime = "NA";
  String endTime = "NA";
  String trainScheduledAt = "NA";
  int journeyDays = 2;
  List<String> refreshedTimes = [];
  String expectedChartingTime = "NA";
  String loadedAtRecent = "NA";
  String loadedAtEarliest = "NA";
  String ehkName = "Na";
  String depot = "Na";
  bool isRunningDay = false;
  String lastFetchedFormattedDate = "";
  bool lastStationLocationFetched = false;
  final TextEditingController _dateController = TextEditingController();
  DateTime selectedDate = DateTime.now();
  List<String> _trainNumbers = [];
  Map<String, String> stationNames = {};
  String? token;
  Map<String, int> stationAttendanceCount = {};
  OnboardingResponse onboardingResponse = OnboardingResponse(
      message: '',
      stations: [],
      lastLocationFetched: null,
      lastLocationFetchedFromUser: null,
      date: null,
      trainNumber: null,
      coachNumbers: [],
      details: {},
      detailsOffBoarding: {},
      detailsOffBoardingInroute: {},
      detailsInroute: {},
      detailsOffBoardingCancelled: {},
      detailsOnBoardingCancelled: {});

  List<String> onboardingStations = [];
  List<String> coachNumbers = [];
  TrainChartingResponse? chartData;
  List<String> attendanceStations = [];
  final ScrollController _scrollController = ScrollController();
  bool _isDownloading = false;
  String locationStatus = "Checking location...";
  bool _insideTrain = false;
  bool trainLoading = false;
  bool loadingOnbordingDetails = false;
  String insideTrainNumber = "";
  String insideTrainDate = "";
  List<String> insideTraincoachNumbers = [];
  EditTrainsData? trainData;
  List<int> frequency = [];
  String userType = "";
  String userName = "";
  int? returnGapDays;
  bool newInsideTrain = false;
  bool _isTogglingTrainStatus = false;
  bool isLoading = false;
  // app update
  AppUpdateInfo? _updateInfo;
  bool _flexibleUpdateAvailable = false;
  bool isLocationRestriction = false;
  bool isBufferTimeRestriction = false;
  bool isMediaUploadEnabled = false;
  List<String> fetchCoachNumbers() {
    if (insideTrainNumber == selectedTrain &&
        DateTime.parse(insideTrainDate) == selectedDate) {
      return insideTraincoachNumbers;
    } else {
      return coachNumbers;
    }
  }

  bool _showStatusPopup = false;
  String? _selectedStatus;
  String? _journeyStatus;
  bool _isUpdatingStatus = false;
  final List<String> _journeyStatusOptions = [
    'scheduled',
    'started',
    'in_progress',
    'completed',
  ];

  // Status icon mapping
  final Map<String, IconData> _statusIcons = {
    'scheduled': Icons.schedule,
    'started': Icons.directions_train,
    'in_progress': Icons.directions_train,
    'completed': Icons.check_circle,
  };

  Timer? _locationTimer; // 👈 Add this line
  String? _lastAlertedStation;

  @override
  void initState() {
    super.initState();
    _checkForUpdate();
    final userModel = Provider.of<UserModel>(context, listen: false);
    token = userModel.token;
    userType = userModel.userType;
    userName = userModel.userName;
    _setInitialDataFromUserModel();
    _requestPermissions();
    _fetchTrainNumbers();
    checkLocationStatus();
    _checkAndShowChangeEmailModal(token!);
    _fetchInsideTrainStatus();
    if (selectedTrain != null) {
      fetchChartingTime();
    }
    fetchInsideTrainDetails();
    _fetchInsideTrainStatus();
    _startLocationUpdates();
  }

  void _setInitialDataFromUserModel() {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final initialTrainNo = userModel.trainNo ?? '';
    final initialSelectedDate = userModel.selectedDate ?? '';
    if (initialSelectedDate.isNotEmpty) {
      try {
        setState(() {
          selectedDate = DateTime.parse(initialSelectedDate);
        });
        _updateDateController();
      } catch (e) {
        print('Error parsing initial date: $e');
      }
    }

    if (initialTrainNo.isNotEmpty) {
      setState(() {
        selectedTrain = initialTrainNo;
      });
      _updateTrainNo(initialTrainNo);
    }
  }

  void _updateDateController() {
    if (selectedDate != null) {
      _dateController.text = _formatDateToCustomFormat(selectedDate!);
    }
  }

  void _updateTrainNo(String trainNumber) async {
    if (trainNumber != null) {
      setState(() {
        selectedTrain = trainNumber;
      });
      final userModel = Provider.of<UserModel>(context, listen: false);
      userModel.setTrainNo(trainNumber);
      await fetchInsideTrainDetails();
      await _fetchInsideTrainStatus();

      if (selectedDate != null) {
        _fetchAttendanceStations(trainNumber);
        fetchAttendance();
        fetchChartingTime();
        _fetchOnboardingDetails(
            trainNumber, DateFormat('yyyy-MM-dd').format(selectedDate));
        fetchReturnGapDay(trainNumber);
      }
    }
  }

  Color _getStatusColorForOption(String status) {
    switch (status) {
      case 'scheduled':
        return Colors.blue;
      case 'started':
        return Colors.orange;
      case 'in_progress':
        return Colors.orange;
      case 'completed':
        return Colors.green;
      default:
        return Colors.blue;
    }
  }

  // Helper method to get icon for a specific status option
  Icon _getStatusIconForOption(String status) {
    switch (status) {
      case 'scheduled':
        return const Icon(Icons.schedule);
      case 'started':
        return const Icon(Icons.directions_train);
      case 'in_progress':
        return const Icon(Icons.directions_train);
      case 'completed':
        return const Icon(Icons.check_circle);
      default:
        return const Icon(Icons.directions_train);
    }
  }

  // Helper method to get display name for status
  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'scheduled':
        return 'Scheduled';
      case 'started':
        return 'Started';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      default:
        return status;
    }
  }

  Color _getStatusColor() {
    switch (_journeyStatus) {
      case 'scheduled':
        return Colors.blue;
      case 'started':
        return Colors.orange;
      case 'in_progress':
        return Colors.orange; // Same as started
      case 'completed':
        return Colors.green;
      default:
        return Colors.blue;
    }
  }

  Widget _getStatusIcon() {
    switch (_journeyStatus) {
      case 'scheduled':
        return const Icon(Icons.schedule, color: Colors.white);
      case 'started':
        return const Icon(Icons.directions_train, color: Colors.white);
      case 'in_progress':
        return const Icon(Icons.directions_train, color: Colors.white);
      case 'completed':
        return const Icon(Icons.check_circle, color: Colors.white);
      default:
        return const Icon(Icons.directions_train, color: Colors.white);
    }
  }

  Future<void> _updateJourneyStatus(String? newStatus) async {
    if (newStatus == null || selectedTrain == null) return;
    setState(() {
      _isUpdatingStatus = true;
    });
    try {
      // Call API to update journey status
      await AttendanceService.updateJourneyStatus(
        token!,
        selectedTrain!,
        DateFormat('yyyy-MM-dd').format(selectedDate),
        newStatus,
      );

      setState(() {
        _journeyStatus = newStatus;
        _showStatusPopup = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                'Journey status updated to ${_getStatusDisplayName(newStatus)}')),
      );
    } catch (e) {
      showInfoModal(context, e.toString(), "Info", () {});
    } finally {
      setState(() {
        _isUpdatingStatus = false;
      });
    }
  }

  Future<void> _fetchJourneyStatus() async {
    print(
        "Fetching journey status for train: $selectedTrain on date: ${DateFormat('yyyy-MM-dd').format(selectedDate)}");
    if (selectedTrain == null) return;

    try {
      final status = await AttendanceService.getJourneyStatus(
        token!,
        selectedTrain!,
        DateFormat('yyyy-MM-dd').format(selectedDate),
      );

      setState(() {
        _journeyStatus = status['journey_status'];
      });
    } catch (e) {
      print('Error fetching journey status: $e');
    }
  }

  String _formatDateToCustomFormat(DateTime date) {
    final day = date.day.toString().padLeft(2, '0');
    final month = DateFormat('MMM').format(date);
    final year = date.year.toString();
    return '$day-$month-$year';
  }

  bool _isSameDate(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  void _requestPermissions() async {
    final permissionHandler = PermissionHandlerService();
    await permissionHandler.requestLocationPermission(context);
  }

  void _toggleInsideTrainStatus(bool value) async {
    print("toggle inside train status: $value");

    try {
      setState(() {
        _isTogglingTrainStatus = true;
      });

      print("Toggle Inside Train Status: $value");

      // print(
      //     "Selected Train: $selectedTrain, Inside Train Number: $insideTrainNumber, Inside Train Date: $insideTrainDate, Selected Date: $selectedDate, Same date: ${_isSameDate(DateTime.parse(insideTrainDate), selectedDate)} , inside train status: $_insideTrain, new inside train status: $newInsideTrain");

      if (selectedTrain == null || selectedTrain!.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please select a train first')),
        );
        return;
      }

      if (_insideTrain == true &&
          insideTrainNumber.isNotEmpty &&
          insideTrainDate.isNotEmpty &&
          (insideTrainNumber != selectedTrain ||
              !_isSameDate(DateTime.parse(insideTrainDate), selectedDate))) {
        await ProfileTrainServices.toggleInsideTrain(
          token!,
          false,
          insideTrainNumber,
        );

        showInfoModal(
          context,
          ' Previous inside train for train $insideTrainNumber on $insideTrainDate has been disabled. Now you can go inside the new train.',
          'Info',
          () {},
        );
        // Wait briefly to avoid race conditions
        await Future.delayed(const Duration(milliseconds: 500));
      }

      if (value == true) {
        await TrainService.addTrainDetails(
            trainNumber: selectedTrain!,
            coachNumbers: coachNumbers,
            originDate: DateFormat('yyyy-MM-dd').format(selectedDate!),
            token: token!);
      }

      await ProfileTrainServices.toggleInsideTrain(
        token!,
        value,
        selectedTrain ?? insideTrainNumber,
      );

      print('Inside Train Status Updated: $value');

      setState(() {
        _insideTrain = value;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(value
              ? 'You are now marked as inside the train'
              : 'You are now marked as outside the train'),
        ),
      );
    } catch (e) {
      print('Error updating train status: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to update train status')),
      );
    } finally {
      // Refresh inside train details after toggling
      setState(() {
        _isTogglingTrainStatus = false;
      });
      fetchInsideTrainDetails();
      _fetchInsideTrainStatus();
    }
  }

  Future<int?> fetchReturnGapDay(String trainId) async {
    try {
      final tempReturnGapData =
          await ReturnGapService.fetchReturnGapByTrain(trainId);
      setState(() {
        returnGapDays = tempReturnGapData?.days;
      });
      //print('Return Gap Days: $returnGapDays');
      return returnGapDays;
    } catch (e) {
      print('Error fetching return gap days: $e');
      setState(() {
        returnGapDays = null;
      });
      return null;
    }
  }

  bool isArrivalWithinBufferTime(String stationName, EditTrainsData trainData,
      int diffTime, String startEndTime) {
    if (startEndTime == null || startEndTime == "") {
      return false;
    }
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userType = userModel.userType;

    if (["railway admin", "railway officer", "s2 admin"].contains(userType)) {
      return true;
    }

    try {
      final now = DateTime.now();
      final arrivalTimeStr = startEndTime;
      DateTime arrival;
      // print(lastStationLocationFetched);
      final parts = arrivalTimeStr.split(":");
      if (lastStationLocationFetched) {
        DateTime futureBase;
        if (returnGapDays != null) {
          futureBase = selectedDate.add(Duration(days: returnGapDays!));
          // print("Return Gap Days: $returnGapDays");
        } else {
          futureBase = selectedDate.add(Duration(days: journeyDays - 1));
        }
        arrival = DateTime(
          futureBase.year,
          futureBase.month,
          futureBase.day,
          int.parse(parts[0]),
          int.parse(parts[1]),
        );
      } else {
        print("selectedDate: $selectedDate");
        print("insideTrainDate: $insideTrainDate");
        print("isBufferTimeRestriction: $isBufferTimeRestriction");
        if (insideTrainDate.isNotEmpty || insideTrainDate != "") {
          DateTime insidedate = DateTime.parse(insideTrainDate);
          arrival = DateTime(
            insidedate.year,
            insidedate.month,
            insidedate.day,
            int.parse(parts[0]),
            int.parse(parts[1]),
          );
        } else {
          arrival = DateTime(
            selectedDate.year,
            selectedDate.month,
            selectedDate.day,
            int.parse(parts[0]),
            int.parse(parts[1]),
          );
        }
      }
      final difference = (arrival.difference(now).inMinutes).abs();
      // print("Station: $stationName , Difference : $difference");
      return difference >= 0 && difference <= diffTime;
    } catch (e) {
      return false;
    }
  }

  void _onTrainNumberChanged(String? trainNumber) async {
    if (trainNumber != null) return;

    setState(() {
      selectedTrain = trainNumber;
    });
    fetchInsideTrainDetails();
    final userModel = Provider.of<UserModel>(context, listen: false);
    userModel.setTrainNo(trainNumber!);

    if (selectedDate != null) {
      _fetchAttendanceStations(trainNumber);
      fetchAttendance();
      fetchChartingTime();
      _fetchOnboardingDetails(
          trainNumber, DateFormat('yyyy-MM-dd').format(selectedDate));
      fetchReturnGapDay(trainNumber);
    }
  }

  @override
  void dispose() {
    _locationTimer?.cancel();
    _scrollController.dispose();
    _dateController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _showForceUpdateDialog() async {
    if (!mounted) return;

    return SystemChannels.platform
        .invokeMethod<void>('SystemNavigator.preventPopInvokeMethod')
        .then((_) {
      if (!mounted) return;

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => WillPopScope(
          onWillPop: () async => false,
          child: AlertDialog(
            title: const Text('Update Required'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.system_update, size: 50, color: Colors.blue),
                SizedBox(height: 16),
                Text(
                  'A new version of the app is available. You must update to continue using the app.',
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            actions: [
              ElevatedButton(
                onPressed: () async {
                  try {
                    await InAppUpdate.performImmediateUpdate();
                  } catch (e) {
                    if (mounted) {
                      _showForceUpdateDialog();
                    }
                  }
                },
                child: const Text('Update Now'),
              ),
            ],
          ),
        ),
      );
    });
  }

  Future<void> _downloadAttendanceDetails() async {
    if (selectedTrain == null || selectedDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a train number and date.'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final userToken = userModel.token;

      // Fetch all necessary data
      await Future.wait([
        updateStations(selectedTrain!), // Stoppages
        _fetchTrainDetails(selectedTrain!), // Timings
        _fetchAttendanceStations(selectedTrain!), // Attendance stoppages
        fetchAttendance(), // Attendance counts
        fetchChartingTime(), // Charting times
        _fetchOnboardingDetails(selectedTrain!,
            DateFormat('yyyy-MM-dd').format(selectedDate)), // Passenger chart
        fetchReturnGapDay(selectedTrain!), // Return gap days
        _saveTrainLocation(), // Location update
        checkLocationStatus(), // Location status
      ] as Iterable<Future>);

      // Update UserModel with selected train and date
      userModel.setTrainNo(selectedTrain!);
      userModel.setSelectedDate(
          "${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}");

      // Scroll to the nearest onboarding station
      if (onboardingStations.isNotEmpty) {
        int middleIndex = (onboardingStations.length / 2).floor();
        String nearestStation = onboardingStations[middleIndex];
        scrollToCurrentStation(nearestStation);
      }

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('All details updated successfully.'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      // ScaffoldMessenger.of(context).showSnackBar(
      //   SnackBar(
      //     content: Text('Failed to update details: $e'),
      //     backgroundColor: Colors.red,
      //     duration: Duration(seconds: 3),
      //   ),
      // );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> checkLocationStatus() async {
    try {
      bool isLocationEnabled = await Geolocator.isLocationServiceEnabled();
      setState(() {
        locationStatus =
            isLocationEnabled ? "Location is ON" : "Location is OFF";
      });
    } catch (e) {
      setState(() {
        locationStatus = "Error checking location: $e";
      });
    }
  }

  void scrollToCurrentStation(String currentStation) {
    if (stations.isEmpty) return;

    final stationIndex = stations.indexOf(currentStation);
    if (stationIndex != -1) {
      final scrollPosition = stationIndex * 100.0;

      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollController.animateTo(
          scrollPosition,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      });
    }
  }

  Future<void> _fetchAttendanceStations(String trainNo) async {
    try {
      final stationAttendanceList =
          await EditTrainServices.fetchStoppagesForAttendanceByTrainNo(
              trainNo, token!);
      if (stationAttendanceList != null) {
        setState(() {
          attendanceStations =
              stationAttendanceList.attendanceStations ?? ['N/A'];
        });
      }
    } catch (e) {
      print('Error fetching train details: $e');
    }
  }

  Future<void> _checkForUpdate() async {
    try {
      AppUpdateInfo updateInfo = await InAppUpdate.checkForUpdate();
      if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
        await _showForceUpdateDialog();
        return;
      }

      try {
        updateInfo = await InAppUpdate.checkForUpdate();
        if (updateInfo.updateAvailability ==
            UpdateAvailability.updateAvailable) {
          setState(() {
            _updateInfo = updateInfo;
            _flexibleUpdateAvailable = true;
          });
        }
      } catch (e) {
        print("Flexible update failed: $e");
      }
    } catch (e) {
      print("Error checking for update: $e");
    }
  }

  Future<void> _startFlexibleUpdate() async {
    if (_updateInfo != null && _flexibleUpdateAvailable) {
      try {
        await InAppUpdate.startFlexibleUpdate();
      } catch (e) {
        print("Error starting flexible update: $e");
      }
    }
  }

  Future<void> _checkAndShowChangeEmailModal(String token) async {
    final prefs = await SharedPreferences.getInstance();
    final lastShownTime = prefs.getInt('lastChangeEmailModalTime');
    final currentTime = DateTime.now().millisecondsSinceEpoch;

    if (lastShownTime == null ||
        currentTime - lastShownTime >= 2 * 60 * 60 * 1000) {
      final profileResponse = await ProfileService.getProfile(token);
      final email = await profileResponse.user?.email ?? '';
      _showChangeEmailModalOnce(email);

      prefs.setInt('lastChangeEmailModalTime', currentTime);
    }
  }

  Future<void> _showChangeEmailModalOnce(String email) async {
    if (email.startsWith("noemail")) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showDialog(
          context: context,
          builder: (BuildContext context) => ChangeEmailModal(),
        );
      });
    }
  }

  Future<void> _fetchTrainNumbers() async {
    try {
      setState(() {
        trainLoading = true;
      });
      _trainNumbers = await TrainService.getTrainNumbers();
      setState(() {
        trainLoading = false;
      });
    } catch (e) {
      print('Error fetching train numbers: $e');
      setState(() {
        trainLoading = false;
      });
    }
  }

  Future<void> _fetchOnboardingDetails(String trainNumber, String date) async {
    try {
      onboardingResponse =
          await OnboardingService.fetchOnboardingDetails(trainNumber, date);
      setState(() {
        onboardingResponse = onboardingResponse;
        onboardingStations = onboardingResponse.stations ?? [];
        coachNumbers = onboardingResponse.coachNumbers ?? [];
      });
    } catch (e) {
      setState(() {
        onboardingResponse = OnboardingResponse(
            message: '',
            stations: [],
            lastLocationFetched: null,
            date: null,
            trainNumber: null,
            coachNumbers: [],
            details: {},
            detailsOffBoarding: {},
            detailsOffBoardingInroute: {},
            detailsInroute: {},
            detailsOffBoardingCancelled: {},
            detailsOnBoardingCancelled: {});
        onboardingStations = [];
      });
      print('Error fetching onboarding details: $e');
    }
  }

  Map<String, List<int>>? getStationoffboardingDetailsCancelled(
      String stationCode) {
    return onboardingResponse.detailsOffBoardingCancelled?[stationCode];
  }

  Map<String, List<int>>? getStationonboardingDetailsCancelled(
      String stationCode) {
    return onboardingResponse.detailsOnBoardingCancelled?[stationCode];
  }

  Map<String, List<int>>? getStationOnboardingDetails(String stationCode) {
    return onboardingResponse.details?[stationCode];
  }

  Map<String, List<int>>? getStationoffboardingDetailsInroute(
      String stationCode) {
    return onboardingResponse.detailsOffBoardingInroute?[stationCode];
  }

  Map<String, List<int>>? getStationOffboardingDetails(String stationCode) {
    return onboardingResponse.detailsOffBoarding?[stationCode];
  }

  Map<String, List<int>>? getStationVacantDetails(String stationCode) {
    return onboardingResponse.detailsVacant?[stationCode];
  }

  Map<String, List<int>>? getStationonboardingDetailsInroute(
      String stationCode) {
    return onboardingResponse.detailsInroute?[stationCode];
  }

  Future<void> _fetchTrainDetails(String trainNo) async {
    try {
      final trainDetails =
          await EditTrainServices.fetchTrainDetailsByTrainNo(trainNo);
      if (trainDetails != null) {
        setState(() {
          startTime = trainDetails.startTime ?? 'NA';
          endTime = trainDetails.endTime ?? 'NA';
          journeyDays = trainDetails.journeyDurationDays ?? 1;
          trainData = trainDetails;
          frequency = trainDetails.frequency ?? [];
          isBufferTimeRestriction =
              trainDetails.isBufferTimeRestriction ?? false;
          isLocationRestriction = trainDetails.isLocationRestriction ?? false;
          isMediaUploadEnabled = trainDetails.isMediaUploadEnabled ?? false;
        });
      }
    } catch (e) {
      print('Error fetching train details: $e');
    }
  }

  String formatTimeinHHMM(String time) {
    List<String> parts = time.split(":");
    if (parts.length >= 2) {
      return "${parts[0]}:${parts[1]}";
    }
    return time;
  }

  Future<void> updateStations(String trainNumber) async {
    final result = await TrainService.getTrainStations(trainNumber);
    List<String> _stations = result['stationList'];
    final Map<String, String> _stationNames = result['stationsDict'];
    setState(() {
      stationNames = _stationNames;
      stations = _stations;
    });
  }

  Future<void> _selectDate() async {
    final DateTime today = DateTime.now();
    DateTime tempSelectedDate = selectedDate ?? today;

    DateTime? _selectedDate = await showDialog<DateTime>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setStateDialog) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: const ColorScheme.light(
                  primary: Colors.blue,
                  onPrimary: Colors.white,
                  onSurface: Colors.black,
                ),
              ),
              child: AlertDialog(
                contentPadding: EdgeInsets.zero,
                content: Container(
                  width: 320,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Select date',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Text(
                                  DateFormat('EEE, MMM d')
                                      .format(tempSelectedDate),
                                  style: const TextStyle(
                                    color: Colors.black,
                                    fontSize: 32,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                const Spacer(),
                                IconButton(
                                  onPressed: () {},
                                  icon: const Icon(Icons.edit, size: 20),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const Divider(height: 1),
                      Container(
                        height: 320,
                        child: CalendarDatePicker(
                          initialDate: tempSelectedDate,
                          key: ValueKey(tempSelectedDate),
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2100),
                          currentDate: today,
                          onDateChanged: (DateTime date) {
                            setStateDialog(() {
                              tempSelectedDate = date;
                            });
                          },
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 8),
                        child: Row(
                          children: [
                            TextButton(
                              onPressed: () {
                                setStateDialog(() {
                                  tempSelectedDate = today;
                                });
                              },
                              child: const Text(
                                'Today',
                                style: TextStyle(
                                  color: Colors.blue,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const Spacer(),
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              child: const Text(
                                'Cancel',
                                style: TextStyle(color: Colors.grey),
                              ),
                            ),
                            const SizedBox(width: 8),
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop(tempSelectedDate);
                              },
                              child: const Text(
                                'OK',
                                style: TextStyle(
                                  color: Colors.blue,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );

    if (tempSelectedDate != null && tempSelectedDate != selectedDate) {
      setState(() {
        selectedDate = tempSelectedDate;
      });

      if (selectedTrain != null) {
        fetchAttendance();
        fetchChartingTime();
        _fetchOnboardingDetails(
            selectedTrain!, DateFormat('yyyy-MM-dd').format(selectedDate));
        fetchReturnGapDay(selectedTrain!);
        _fetchInsideTrainStatus();
        fetchInsideTrainDetails();
        await _fetchJourneyStatus();
      }

      Provider.of<UserModel>(context, listen: false).setSelectedDate(
          "${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}");
    }
  }

  void fetchAttendance() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;
    try {
      final attendanceCount =
          await AttendanceService.fetchStationAttendanceCount(
              trainNumber: selectedTrain!,
              date: DateFormat('yyyy-MM-dd').format(selectedDate),
              token: userToken);

      setState(() {
        stationAttendanceCount = attendanceCount;
      });

      print('Attendance Count: $attendanceCount');
    } catch (e) {
      print('Error: $e');
    }
  }

  void fetchChartingTime() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;
    try {
      final chartResponse = await TrainService.fetchChartingTime(
          trainNumber: selectedTrain,
          date: DateFormat('yyyy-MM-dd').format(selectedDate),
          token: userToken);
      setState(() {
        chartData = chartResponse;
        depot = chartResponse!.depot;
        ehkName = chartResponse.ehkDict;
        trainScheduledAt = chartResponse.startTime;
        refreshedTimes = chartResponse.refreshedTimes;
        expectedChartingTime = chartResponse.chartingTime;
        loadedAtEarliest = chartResponse.loadedAtEarliest;
        loadedAtRecent = chartResponse.loadedAtRecent;
        isRunningDay = chartResponse.isRunningDay;
      });
      _fetchTrainDetails(selectedTrain!);
      _showTrainStatusModal(isRunningDay);
      fetchReturnGapDay(selectedTrain!);
      //print(chartResponse!.chartingTime);
    } catch (e) {
      print('Error: $e');
    }
  }

  void _showTrainStatusModal(bool isRunning) {
    String dayOfWeek = DateFormat('EEEE').format(selectedDate);
    if (!isRunning) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.red,
            title: const Text(
              "Train Not Running",
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 16.0,
                  fontWeight: FontWeight.bold),
            ),
            content: Text(
              "Train $selectedTrain is NOT running on $dayOfWeek\nRunning Days: ${_formatRunningDays()}",
              style: const TextStyle(color: Colors.black, fontSize: 15.0),
            ),
            actions: <Widget>[
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade100,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'OK',
                  style: TextStyle(
                      fontSize: 14.0,
                      color: Colors.blue,
                      fontWeight: FontWeight.bold),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        },
      );
    }
  }

  Future<void> fetchInsideTrainDetails() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final userToken = userModel.token == null
        ? prefs.getString('authToken')
        : userModel.token;
    try {
      final trainDetails =
          await AttendanceService.fetchInsideTrainDetails(userToken);
      setState(() {
        selectedTrain = trainDetails["insideTrainNumber"];
        selectedDate = DateTime.parse(trainDetails["date"]);
        insideTrainNumber = trainDetails["insideTrainNumber"];
        selectedTrain = trainDetails["insideTrainNumber"];
        insideTrainDate = trainDetails["date"];
        insideTraincoachNumbers = trainDetails["coachNumbers"];
      });
      if (trainDetails["insideTrainNumber"] != null &&
          trainDetails["date"] != null) {
        fetchAttendance();
        updateStations(trainDetails["insideTrainNumber"]);
        _fetchTrainDetails(trainDetails["insideTrainNumber"]);
        _fetchOnboardingDetails(
            trainDetails["insideTrainNumber"], trainDetails["date"]);
        _fetchAttendanceStations(trainDetails["insideTrainNumber"]);
        fetchChartingTime();
        _fetchJourneyStatus();
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  Future<void> _fetchInsideTrainStatus() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final userToken = userModel.token == null
        ? prefs.getString('authToken')
        : userModel.token;
    try {
      print('Fetching inside train status for token: $userToken');
      final trainDetails =
          await AttendanceService.fetchInsideTrainDetails(userToken);
      final insideTrainStatus =
          await ProfileTrainServices.getInsideTrainStatus(userToken!);

      setState(() {
        _insideTrain = insideTrainStatus['inside_train'];
        // insideTrainNumber = insideTrainStatus['inside_train_number'] ?? "";
        // insideTrainDate = insideTrainStatus['inside_train_date'] ?? "";
      });
    } catch (e) {
      print('Error fetching toggle switch statuses: $e');
    }
  }

  Future<void> _handleRefresh() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;

    setState(() => _isDownloading = true);

    try {
      if (selectedTrain != null) {
        await _saveTrainLocation();
        fetchAttendance();
        await _fetchOnboardingDetails(
          selectedTrain!,
          DateFormat('yyyy-MM-dd').format(selectedDate),
        );
        await _fetchTrainDetails(selectedTrain!);
        await _fetchAttendanceStations(selectedTrain!);
        fetchChartingTime();
        await checkLocationStatus();
        await _saveTrainLocation();
        fetchReturnGapDay(selectedTrain!);
        _fetchJourneyStatus();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Data refreshed successfully')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Refresh failed: ${e.toString()}')),
      );
    } finally {
      setState(() => _isDownloading = false);
    }
  }

  void _startLocationUpdates() {
    _locationTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      // _saveTrainLocation();
    });
  }

  Future<void> showLocalNotification(String title, String body) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'station_alerts_channel',
      'Station Alerts',
      channelDescription: 'Alerts when train is near a station',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: true,
    );

    const NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);

    await flutterLocalNotificationsPlugin.show(
      DateTime.now().millisecondsSinceEpoch ~/ 1000, // unique ID
      title,
      body,
      platformChannelSpecifics,
    );
  }

// M
 Future<void> _saveTrainLocation() async {
  final userModel = Provider.of<UserModel>(context, listen: false);
  final userToken = userModel.token;

  try {
    // ✅ Get real-time location
    Position position = await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );

    final double currentLatitude = position.latitude;
    final double currentLongitude = position.longitude;

    await LocationService.saveTrainLocation(
      userToken,
      currentLatitude.toString(),
      currentLongitude.toString(),
    );

    // 🔍 Find nearby stations within 50km
    List<String> nearbyStations = [];

    for (var stoppage in trainData?.stoppagesWithLatLng ?? []) {
      final lat = stoppage.latitude;
      final lng = stoppage.longitude;

      if (lat != null && lng != null) {
        final distance = Geolocator.distanceBetween(
          currentLatitude,
          currentLongitude,
          lat,
          lng,
        );
        if (distance <= 50000) {
          nearbyStations.add(stoppage.stationName ?? stoppage.stationCode);
        }
      }
    }

    if (nearbyStations.isNotEmpty && context.mounted) {
      String currentNearbyStation = nearbyStations.first;
      final assignedCoaches = onboardingResponse?.coachNumbers ?? [];

      if (assignedCoaches.isNotEmpty) {
        if (currentNearbyStation != _lastAlertedStation) {
          _lastAlertedStation = currentNearbyStation;

          await showLocalNotification(
            "You're near: $currentNearbyStation",
            "Tap to view train or station updates.",
          );

          await showDialog(
            context: context,
            builder: (ctx) {
              return AlertDialog(
                title: const Text("🛤️ Nearby Station Alert"),
                content: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text("You're near the following station(s):"),
                      const SizedBox(height: 8),
                      ...nearbyStations.take(3).map((station) => Text("• $station")),
                      const Divider(),
                      const SizedBox(height: 8),
                      const Text("🚆 Assigned Coaches:", style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 6),
                      Text("Coaches: ${assignedCoaches.join(', ')}"),
                      const Divider(),
                      const SizedBox(height: 6),
                      ...assignedCoaches.map((coach) {
                        final onboard = (onboardingResponse?.details?[coach] ?? []) as List;
                        final deboard = (onboardingResponse?.detailsOffBoarding?[coach] ?? []) as List;

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("🚃 Coach: $coach", style: const TextStyle(fontWeight: FontWeight.bold)),
                            const SizedBox(height: 4),
                            if (onboard.isNotEmpty) ...[
                              const Text("🟢 Onboarding:", style: TextStyle(color: Colors.green)),
                              ...onboard.map((e) => Text("- ${e.toString()}")),
                            ] else
                              const Text("🟢 Onboarding: None", style: TextStyle(color: Colors.green)),
                            const SizedBox(height: 6),
                            if (deboard.isNotEmpty) ...[
                              const Text("🔴 Deboarding:", style: TextStyle(color: Colors.red)),
                              ...deboard.map((e) => Text("- ${e.toString()}")),
                            ] else
                              const Text("🔴 Deboarding: None", style: TextStyle(color: Colors.red)),
                            const Divider(),
                          ],
                        );
                      }).toList(),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    child: const Text("OK"),
                    onPressed: () => Navigator.of(ctx).pop(),
                  ),
                ],
              );
            },
          );
        } else {
          print("ℹ️ Already alerted for station: $currentNearbyStation");
        }
      } else {
        print("⛔ No coach assigned to this user — skipping notification");
      }
    } else {
      print("ℹ️ No nearby stations within 50km");
    }
  } catch (e) {
    print("❌ Error saving location: $e");
  }
}

  bool _showDetails = false;

  String? getArrivalTimeForStation(
      String stationName, EditTrainsData trainData) {
    final arrival = trainData.arrivalTime?.firstWhere(
        (map) => map["station_code"] == stationName,
        orElse: () => {});

    return arrival != null && arrival.isNotEmpty
        ? arrival["arrival_time"]
        : null;
  }

  Widget _buildLastFetchedTime() {
    String formattedTime = "";
    String? locationFetchedFromUser;
    if (onboardingResponse.lastLocationFetched == null) {
      formattedTime = "location not fetched";
    } else {
      final lastFetched = onboardingResponse.lastLocationFetched!;
      final istTime =
          lastFetched.toUtc().add(const Duration(hours: 5, minutes: 30));
      formattedTime = DateFormat('dd-MM-yyyy HH:mm').format(istTime);
      lastFetchedFormattedDate = DateFormat('dd-MM-yyyy').format(istTime);
      lastStationLocationFetched = onboardingResponse.stations != null &&
          onboardingResponse.stations!.isNotEmpty &&
          stations.isNotEmpty &&
          onboardingResponse.stations!.contains(stations[stations.length - 1]);
    }

    (onboardingResponse.lastLocationFetchedFromUser == null)
        ? locationFetchedFromUser = "NA"
        : locationFetchedFromUser =
            (onboardingResponse.lastLocationFetchedFromUser);

    return Positioned(
      left: 5,
      bottom: 5,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.6),
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          'Last fetched: $formattedTime\nFrom User: $locationFetchedFromUser',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  String formatTime(DateTime ogTime) {
    final istTime = ogTime.toUtc().add(const Duration(hours: 5, minutes: 30));
    String formattedTime = DateFormat('dd-MM-yyyy HH:mm').format(istTime);
    return formattedTime;
  }

  Widget _buildChartingTime() {
    String formattedTime = "";
    String? locationFetchedFromUser;
    if (onboardingResponse.lastLocationFetched == null) {
      formattedTime = "location not fetched";
    } else {
      final lastFetched = onboardingResponse.lastLocationFetched!;
      formattedTime = formatTime(lastFetched);
    }

    return Positioned(
      left: 5,
      top: 110,
      child: GestureDetector(
        onTap: () {
          setState(() {
            _showDetails = !_showDetails;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.5),
            borderRadius: BorderRadius.circular(2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: _showDetails
              ? Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(width: 4),
                    const SizedBox(width: 4),
                    Text(
                      'Expected Charting Time: $trainScheduledAt',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Charting started at: ${loadedAtEarliest != "NA" ? formatTime(DateTime.parse(loadedAtEarliest)) : "NA"}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Charting refreshed at: ${loadedAtRecent != "NA" ? formatTime(DateTime.parse(loadedAtRecent)) : "NA"}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    SizedBox(
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(
                          maxHeight: 100,
                        ),
                        child: SingleChildScrollView(
                          child: Column(
                            children: refreshedTimes.map((time) {
                              return Row(
                                children: [
                                  const SizedBox(width: 4),
                                  Text(
                                    'Charting Time: $time',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ),
                    Text(
                      'Train Depot:$depot',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'EHK Assigned for train:$ehkName',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'User Location:$locationStatus',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                )
              : const Icon(
                  Icons.access_time,
                  size: 14,
                  color: Colors.white,
                ),
        ),
      ),
    );
  }

  String _formatRunningDays() {
    if (frequency.isEmpty) return 'N/A';

    final dayAbbreviations = frequency.map((dayIndex) {
      final dayName =
          DateFormat('EEEE').format(DateTime(2023, 1, 1 + dayIndex));
      return dayName.substring(0, 3);
    }).toList();

    if (dayAbbreviations.length == 7) return 'Daily';

    return dayAbbreviations.join(', ');
  }

  @override
  Widget build(BuildContext context) {
    if (_flexibleUpdateAvailable) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _startFlexibleUpdate();
      });
    }
    return Scaffold(
      appBar: const CustomAppBar(title: "Attendance"),
      drawer: const CustomDrawer(),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        child: Stack(
          children: [
            Column(
              children: [
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                      vertical: 8.0, horizontal: 12.0),
                  decoration: const BoxDecoration(
                    color: Color.fromRGBO(239, 239, 239, 1),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: DropdownSearch<String>(
                          popupProps: PopupProps.menu(
                              showSearchBox: true,
                              searchFieldProps: TextFieldProps(
                                keyboardType: TextInputType.number,
                                inputFormatters: <TextInputFormatter>[
                                  FilteringTextInputFormatter.digitsOnly,
                                  LengthLimitingTextInputFormatter(5),
                                ],
                              )),
                          items: _trainNumbers,
                          dropdownDecoratorProps: const DropDownDecoratorProps(
                            dropdownSearchDecoration: InputDecoration(
                              labelText: "Train Number",
                              labelStyle: TextStyle(fontSize: 14),
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 12),
                            ),
                          ),
                          dropdownButtonProps: DropdownButtonProps(
                            icon: trainLoading
                                ? const Padding(
                                    padding: EdgeInsets.all(8.0),
                                    child: SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                          strokeWidth: 2),
                                    ),
                                  )
                                : const Icon(Icons.arrow_drop_down),
                          ),
                          onChanged: (value) async {
                            selectedTrain = value;
                            updateStations(value!);
                            _fetchTrainDetails(value);
                            if (value != null && selectedDate != null) {
                              _fetchAttendanceStations(value);
                              fetchAttendance();
                              fetchChartingTime();
                              _fetchOnboardingDetails(
                                  selectedTrain!,
                                  DateFormat('yyyy-MM-dd')
                                      .format(selectedDate));
                              await Provider.of<UserModel>(context,
                                      listen: false)
                                  .setTrainNo(value);
                              await _fetchJourneyStatus();
                            }
                          },
                          selectedItem: selectedTrain,
                        ),
                      ),
                      if (_insideTrain)
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: Image.asset(
                            'assets/images/user_inside_train.png',
                            width: 50,
                            height: 50,
                          ),
                        ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectDate(),
                          child: InputDecorator(
                            decoration: const InputDecoration(
                              labelText: "Date",
                              labelStyle: TextStyle(fontSize: 14),
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 12),
                            ),
                            child: Text(
                              "${selectedDate.day.toString().padLeft(2, '0')}-${DateFormat('MMM').format(selectedDate)}-${selectedDate.year}",
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  color: Colors.blueGrey.shade200,
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 20),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Timings",
                              style:
                                  TextStyle(color: Colors.black, fontSize: 12),
                            ),
                            SizedBox(
                              height: 5,
                              width: 25,
                            ),
                            Text(
                              "Stoppages",
                              style:
                                  TextStyle(color: Colors.black, fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(right: 20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              "Passenger Chart   Atten..",
                              style:
                                  TextStyle(color: Colors.black, fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Container(
                    color: const Color.fromRGBO(239, 239, 239, 1),
                    child: ListView.builder(
                      controller: _scrollController,
                      itemCount: stations.length,
                      itemBuilder: (context, index) {
                        final station = stations[index];
                        final arrivalTime = trainData != null
                            ? getArrivalTimeForStation(station, trainData!)
                            : null;
                        bool isAttendanceAllowed = false;
                        if ((stations.length - 1) == index || index == 0) {
                          isAttendanceAllowed = true;
                        } else {
                          isAttendanceAllowed =
                              attendanceStations.contains(station);
                        }

                        final hasOnboardingStations =
                            onboardingStations.isNotEmpty;
                        DateTime? trainStartDateTime;
                        try {
                          final selectedDateStr =
                              DateFormat('yyyy-MM-dd').format(selectedDate);
                          trainStartDateTime = DateFormat('yyyy-MM-dd HH:mm')
                              .parse('$selectedDateStr $startTime');
                        } catch (e) {
                          print('Error parsing start time: $e');
                        }

                        final hasTrainStarted = trainStartDateTime != null &&
                            DateTime.now().isAfter(trainStartDateTime);

                        final isOnboardingStation = hasOnboardingStations
                            ? onboardingStations.contains(station)
                            : (hasTrainStarted && index == 0);

                        final firstOnboardingIndex = stations
                            .indexWhere((s) => onboardingStations.contains(s));

                        final isBeforeOnboarding = firstOnboardingIndex != -1 &&
                            index < firstOnboardingIndex;
                        final isAfterOnboarding = firstOnboardingIndex != -1 &&
                            index > firstOnboardingIndex;
                        final isFirstStationIn12hrBuffer = trainData != null
                            ? isArrivalWithinBufferTime(
                                station, trainData!, 12 * 60, startTime)
                            : false;

                        final isLastStationIn12HourBuffer = trainData != null
                            ? isArrivalWithinBufferTime(
                                stations[stations.length - 1],
                                trainData!,
                                12 * 60,
                                endTime)
                            : false;
                        final isInThirySixHourBuffer = trainData != null
                            ? isArrivalWithinBufferTime(
                                stations[stations.length - 1],
                                trainData!,
                                36 * 60,
                                endTime,
                              )
                            : false;
                        bool isFirstStaion =
                            (stations[index] == stations[0]) ? true : false;
                        bool isLastStaion =
                            (stations[index] == stations[stations.length - 1])
                                ? true
                                : false;
                        bool isFirstAndLastStation =
                            (stations[index] == stations[0] ||
                                    stations[index] ==
                                        stations[stations.length - 1])
                                ? true
                                : false;

                        return StationItem(
                          isFirstStationIn12hrBuffer:
                              isFirstStationIn12hrBuffer,
                          isLastStationIn12HourBuffer:
                              isLastStationIn12HourBuffer,
                          isInThirySixHourBuffer: isInThirySixHourBuffer,
                          isFirstAndLastStation: isFirstAndLastStation,
                          isAttendanceStation: isAttendanceAllowed,
                          arrivalTime: arrivalTime,
                          stationCode: station,
                          isFirstStaion: isFirstStaion,
                          isLastStaion: isLastStaion,
                          stationName: stationNames[station] ?? "station name",
                          attendanceCount: stationAttendanceCount[station] ?? 0,
                          showStartTime: index == 0,
                          showEndTime: index == stations.length - 1,
                          startTime: formatTimeinHHMM(startTime),
                          endTime: formatTimeinHHMM(endTime),
                          selectedTrain: selectedTrain!,
                          selectedDate: DateFormat('yyyy-MM-dd')
                              .format(selectedDate)
                              .toString(),
                          isOnboardingStation: isOnboardingStation,
                          onboardingDetails:
                              getStationOnboardingDetails(station),
                          offboardingDetails:
                              getStationOffboardingDetails(station),
                          vacantDetails: getStationVacantDetails(station),
                          isBeforeOnboarding: isBeforeOnboarding,
                          isAfterOnboarding: isAfterOnboarding,
                          coachNumbers: insideTrainNumber == selectedTrain &&
                                  DateTime.parse(insideTrainDate) ==
                                      selectedDate
                              ? insideTraincoachNumbers
                              : coachNumbers,
                          onboardingDetailsInroute:
                              getStationonboardingDetailsInroute(station),
                          offboardingDetailsInroute:
                              getStationoffboardingDetailsInroute(station),
                          onboardingDetailsCancelled:
                              getStationonboardingDetailsCancelled(station),
                          offboardingDetailsCancelled:
                              getStationoffboardingDetailsCancelled(station),
                          nearbyStations: onboardingStations,
                          userType: userType,
                          userName: userName,
                          token: token!,
                          isBufferTimeRestriction: isBufferTimeRestriction,
                          isLocationRestriction: isLocationRestriction,
                          isMediaUploadEnabled: isMediaUploadEnabled,
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
            _buildLastFetchedTime(),
            _buildChartingTime(),
            Positioned(
              bottom: 80,
              left: 5,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Floating action button
                  FloatingActionButton.small(
                    backgroundColor: _journeyStatus != null
                        ? _getStatusColor() // Use your custom method
                        : Colors.blue,
                    onPressed: () {
                      setState(() {
                        _showStatusPopup = !_showStatusPopup;
                        _selectedStatus = _journeyStatus;
                      });
                    },
                    child: _journeyStatus != null
                        ? _getStatusIcon() // Use your custom method
                        : const Icon(Icons.directions_train,
                            color: Colors.white),
                  ),

                  // Status popup
                  if (_showStatusPopup)
                    SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(-1, 0),
                        end: Offset.zero,
                      ).animate(CurvedAnimation(
                        parent: ModalRoute.of(context)!.animation!,
                        curve: Curves.easeOut,
                      )),
                      child: Container(
                        width: 220,
                        margin: const EdgeInsets.only(top: 10),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black26,
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(12),
                              child: Text(
                                'Update Journey Status',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue.shade800,
                                ),
                              ),
                            ),
                            const Divider(height: 0),
                            ..._journeyStatusOptions.map((status) {
                              return ListTile(
                                leading: Icon(
                                  _statusIcons[status] ??
                                      Icons.directions_train,
                                  color: _getStatusColorForOption(
                                      status), // Use a helper method
                                ),
                                title: Text(_getStatusDisplayName(status)),
                                trailing: _selectedStatus == status
                                    ? const Icon(Icons.check,
                                        color: Colors.green)
                                    : null,
                                onTap: () {
                                  setState(() => _selectedStatus = status);
                                },
                              );
                            }).toList(),
                            const Divider(height: 0),
                            Padding(
                              padding: const EdgeInsets.all(12),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  TextButton(
                                    onPressed: () => setState(
                                        () => _showStatusPopup = false),
                                    child: const Text('Cancel'),
                                  ),
                                  const SizedBox(width: 8),
                                  ElevatedButton.icon(
                                    onPressed: _isUpdatingStatus
                                        ? null
                                        : () => _updateJourneyStatus(
                                            _selectedStatus),
                                    icon: const Icon(Icons.check, size: 20),
                                    label: const Text('Update'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: Container(
        padding: const EdgeInsets.only(right: 12, bottom: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Toggle Button (Inside/Go Train)
            if ([
              "railway admin",
              "war room user",
              "s2 admin",
              "railway officer"
            ].contains(userType))
              ElevatedButton(
                  onPressed: _isTogglingTrainStatus
                      ? null
                      : () {
                          _toggleInsideTrainStatus(!_insideTrain);
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        _insideTrain ? Colors.green[300] : Colors.red[300],
                    foregroundColor: Colors.black,
                    elevation: 6,
                    shadowColor: Colors.black26,
                    shape: const CircleBorder(),
                    padding: EdgeInsets.zero,
                    fixedSize: const Size(40, 40),
                  ),
                  child: _isTogglingTrainStatus
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              _insideTrain ? 'Inside' : 'Go',
                              style: const TextStyle(fontSize: 8),
                            ),
                            Text(
                              _insideTrain ? 'Train' : 'Inside',
                              style: const TextStyle(fontSize: 8),
                            ),
                            if (_insideTrain)
                              Text(
                                insideTrainNumber,
                                style: const TextStyle(
                                  fontSize: 7,
                                  // fontWeight: FontWeight.bold,
                                ),
                              )
                            else
                              const Text(
                                'Train',
                                style: TextStyle(fontSize: 8),
                              ),
                          ],
                        )),

            const SizedBox(width: 12),

            // Update FAB
            FloatingActionButton.small(
              onPressed: isLoading
                  ? null
                  : _insideTrain
                      ? _downloadAttendanceDetails
                      : [
                          "railway admin",
                          "war room user",
                          "s2 admin",
                          "railway officer"
                        ].contains(userType)
                          ? () {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                      'You are not inside the train. Please go inside the train first.'),
                                ),
                              );
                            }
                          : _downloadAttendanceDetails,
              backgroundColor: isLoading
                  ? Colors.grey
                  : !_insideTrain &&
                          [
                            "railway admin",
                            "war room user",
                            "s2 admin",
                            "railway officer"
                          ].contains(userType)
                      ? Colors.grey.shade400
                          .withOpacity(0.5) // faded background
                      : _insideTrain
                          ? Colors.blueGrey.shade200
                          : Colors.grey.shade400,
              child: isLoading
                  ? const SizedBox(
                      width: 14,
                      height: 14,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 1.5,
                      ),
                    )
                  : Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 20,
                          color: !_insideTrain &&
                                  [
                                    "railway admin",
                                    "war room user",
                                    "s2 admin",
                                    "railway officer"
                                  ].contains(userType)
                              ? Colors.black.withOpacity(0.4) // faded icon
                              : Colors.black,
                        ),
                        Text(
                          "Update",
                          style: TextStyle(
                            fontSize: 10,
                            color: !_insideTrain &&
                                    [
                                      "railway admin",
                                      "war room user",
                                      "s2 admin",
                                      "railway officer"
                                    ].contains(userType)
                                ? Colors.black.withOpacity(0.4) // faded text
                                : Colors.black,
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
    );
  }
}

Widget _buildTimeText(String? time) {
  return Text(
    time ?? "-",
    style: const TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.bold,
    ),
    textAlign: TextAlign.right,
  );
}

class StationItem extends StatelessWidget {
  final String stationCode;
  final String stationName;
  final int attendanceCount;
  final bool showStartTime;
  final bool showEndTime;
  final String startTime;
  final String endTime;
  final String selectedDate;
  final String selectedTrain;
  final bool isOnboardingStation;
  final bool isFirstStationIn12hrBuffer;
  final Map<String, List<int>>? onboardingDetails;
  final Map<String, List<int>>? offboardingDetails;
  final Map<String, List<int>>? vacantDetails;
  final bool isAttendanceStation;
  final bool isBeforeOnboarding;
  final bool isAfterOnboarding;
  final bool isLastStationIn12HourBuffer;
  final bool isInThirySixHourBuffer;
  final bool isFirstAndLastStation;
  final bool isLastStaion;
  final bool isFirstStaion;
  final List<String> coachNumbers;
  final String? arrivalTime;
  final Map<String, List<int>>? offboardingDetailsInroute;
  final Map<String, List<int>>? onboardingDetailsInroute;
  final List<String>? nearbyStations;
  final String userType;
  final String userName;
  final String token;
  final Map<String, List<int>>? offboardingDetailsCancelled;
  final Map<String, List<int>>? onboardingDetailsCancelled;
  final bool isLocationRestriction;
  final bool isBufferTimeRestriction;
  final bool isMediaUploadEnabled;

  const StationItem({
    Key? key,
    required this.stationCode,
    required this.stationName,
    required this.attendanceCount,
    this.showStartTime = false,
    this.showEndTime = false,
    required this.startTime,
    required this.isFirstStationIn12hrBuffer,
    required this.isFirstAndLastStation,
    required this.userType,
    required this.userName,
    required this.token,
    required this.endTime,
    required this.selectedDate,
    required this.selectedTrain,
    required this.isOnboardingStation,
    required this.offboardingDetails,
    required this.onboardingDetails,
    required this.vacantDetails,
    required this.isAttendanceStation,
    required this.isBeforeOnboarding,
    required this.isAfterOnboarding,
    required this.coachNumbers,
    required this.isLastStationIn12HourBuffer,
    required this.isInThirySixHourBuffer,
    required this.isLastStaion,
    required this.isFirstStaion,
    this.arrivalTime,
    this.nearbyStations,
    required this.offboardingDetailsInroute,
    required this.onboardingDetailsInroute,
    required this.offboardingDetailsCancelled,
    required this.onboardingDetailsCancelled,
    required this.isLocationRestriction,
    required this.isBufferTimeRestriction,
    required this.isMediaUploadEnabled,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Map<String, Map<String, int>> coachTotals = {};
    Map<String, Map<String, int>> filteredCoachTotals = {};
    Set<String> allCoaches = {
      ...?onboardingDetails?.keys,
      ...?offboardingDetails?.keys,
      ...?vacantDetails?.keys,
    };
    for (String coach in allCoaches) {
      coachTotals[coach] = {
        'onboard': onboardingDetails?[coach]?.length ?? 0,
        'offboard': offboardingDetails?[coach]?.length ?? 0,
        'vacant': vacantDetails?[coach]?.length ?? 0,
      };

      if (coachNumbers.contains(coach)) {
        filteredCoachTotals[coach] = coachTotals[coach]!;
      }
    }

    var filteredEntries = coachTotals.entries
        .where((entry) =>
            entry.value['onboard']! > 0 ||
            entry.value['offboard']! > 0 ||
            entry.value['vacant']! > 0)
        .toList();

    final totalOnboard = filteredEntries
        .map((e) => e.value['onboard']!)
        .fold<int>(0, (sum, count) => sum + count);
    final totalOffboard = filteredEntries
        .map((e) => e.value['offboard']!)
        .fold<int>(0, (sum, count) => sum + count);
    final totalVacant = filteredEntries
        .map((e) => e.value['vacant']!)
        .fold<int>(0, (sum, count) => sum + count);

    filteredEntries = coachTotals.entries
        .where((entry) => coachNumbers.contains(entry.key))
        .toList();

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(width: 10),
        SizedBox(
          width: 80,
          child: Column(
            children: [
              if (showStartTime) _buildTimeText(startTime),
              if (!showStartTime)
                _buildTimeText(
                    arrivalTime?.isNotEmpty == true ? arrivalTime : " "),
              if (showEndTime) _buildTimeText(endTime),
              if (!showEndTime &&
                  !showStartTime &&
                  (arrivalTime == null || arrivalTime!.isEmpty))
                _buildTimeText("-"),
            ],
          ),
        ),
        const SizedBox(width: 10),
        Column(
          children: [
            Stack(
              alignment: showStartTime
                  ? Alignment.topCenter
                  : showEndTime
                      ? Alignment.bottomCenter
                      : Alignment.center,
              children: [
                Container(
                  width: 14,
                  height: showStartTime || showEndTime ? 90 : 100,
                  margin: EdgeInsets.only(
                    top: showStartTime ? 8 : 0,
                    bottom: showEndTime ? 8 : 0,
                  ),
                  decoration: BoxDecoration(
                    color: const Color.fromARGB(255, 198, 223, 239),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(showStartTime ? 8 : 0),
                      topRight: Radius.circular(showStartTime ? 8 : 0),
                      bottomLeft: Radius.circular(showEndTime ? 8 : 0),
                      bottomRight: Radius.circular(showEndTime ? 8 : 0),
                    ),
                  ),
                ),
                Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.green,
                          width: 2,
                        ),
                      ),
                    ),
                    if (isOnboardingStation)
                      Container(
                        width: 16,
                        height: 16,
                        decoration: const BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                      ),
                    if (!isOnboardingStation && isBeforeOnboarding)
                      const Icon(
                        Icons.check,
                        size: 12,
                        color: Colors.green,
                      ),
                  ],
                ),
              ],
            ),
          ],
        ),
        const SizedBox(width: 30),
        Expanded(
          child: InkWell(
            onTapDown: (details) async {
              if ((isLocationRestriction)
                  ? (isAttendanceStation &&
                      nearbyStations!.contains(stationCode))
                  : true) {
                if (((isBufferTimeRestriction)
                    ? !(isFirstAndLastStation)
                    : true)) {
                  final result = await showMenu<String>(
                    context: context,
                    position: RelativeRect.fromLTRB(
                      details.globalPosition.dx,
                      details.globalPosition.dy,
                      details.globalPosition.dx,
                      details.globalPosition.dy,
                    ),
                    items: [
                      const PopupMenuItem(
                        value: 'self',
                        child: Text('Self'),
                      ),
                      const PopupMenuItem(
                        value: 'coach attendent',
                        child: Text('Other CA'),
                      ),
                      const PopupMenuItem(
                        value: 'OBHS',
                        child: Text('Other EHK/OBHS'),
                      ),
                    ],
                  );

                  if (result != null) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ImageUploadPage(
                            selectedOption: result == "self" ? "self" : "other",
                            forUserType: result,
                            trainNumber: selectedTrain,
                            journeyDate: selectedDate,
                            stationCode: stationCode,
                            stationName: stationName,
                            isMediaUploadEnabled: isMediaUploadEnabled),
                      ),
                    );
                  }
                } else {
                  if (isBufferTimeRestriction
                      ? (isFirstStationIn12hrBuffer ||
                          isLastStationIn12HourBuffer ||
                          (userType == "war room user" &&
                              isInThirySixHourBuffer))
                      : true) {
                    final result = await showMenu<String>(
                      context: context,
                      position: RelativeRect.fromLTRB(
                        details.globalPosition.dx,
                        details.globalPosition.dy,
                        details.globalPosition.dx,
                        details.globalPosition.dy,
                      ),
                      items: [
                        const PopupMenuItem(
                          value: 'self',
                          child: Text('Self'),
                        ),
                        const PopupMenuItem(
                          value: 'coach attendent',
                          child: Text('Other CA'),
                        ),
                        const PopupMenuItem(
                          value: 'OBHS',
                          child: Text('Other EHK/OBHS'),
                        ),
                      ],
                    );

                    if (result != null) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ImageUploadPage(
                              selectedOption:
                                  result == "self" ? "self" : "other",
                              forUserType: result,
                              trainNumber: selectedTrain,
                              journeyDate: selectedDate,
                              stationCode: stationCode,
                              stationName: stationName,
                              isMediaUploadEnabled: isMediaUploadEnabled),
                        ),
                      );
                    }
                  } else {
                    String bufferMessage = "";
                    if ((!isLastStationIn12HourBuffer && isLastStaion) &&
                        (isInThirySixHourBuffer && isLastStaion)) {
                      bufferMessage =
                          "The train, Train Number: $selectedTrain is not within 12 hour buffer after reaching the last station."
                          "Attendance can only be marked by a war room user.";
                    } else if (!isFirstStationIn12hrBuffer && isFirstStaion) {
                      bufferMessage =
                          "The train, Train Number: $selectedTrain is not within 12 hour buffer";
                    } else if ((!isLastStationIn12HourBuffer && isLastStaion) &&
                        (!isInThirySixHourBuffer && isLastStaion)) {
                      bufferMessage =
                          "Train Number: $selectedTrain has exceeded the 36-hour buffer after reaching the last station. Attendance cannot be marked.";
                    } else {
                      bufferMessage =
                          "The time between the current time and the train's arrival time is not within the buffer.\n"
                          "If you're unable to see the arrival time correctly, please contact the admin or add the arrival time for train.";
                    }

                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                      content: Text(bufferMessage),
                      backgroundColor: const Color.fromARGB(255, 237, 162, 76),
                      duration: const Duration(seconds: 10),
                    ));
                  }
                }
              } else {
                if (!isAttendanceStation) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                          "Attendance cannot be marked for station $stationCode as it is not an attendance station."),
                      backgroundColor: const Color.fromARGB(255, 237, 162, 76),
                      duration: const Duration(seconds: 2),
                    ),
                  );
                } else if (isLocationRestriction &&
                    (!nearbyStations!.contains(stationCode))) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                          "You're over 50 KM away from the selected station $stationCode. Attendance can only be marked when you're within the allowed range.\nFor Now You can only mark attendance for stations: ${nearbyStations!.join(', ')}"),
                      backgroundColor: const Color.fromARGB(255, 237, 162, 76),
                      duration: const Duration(seconds: 2),
                    ),
                  );
                } else if (isBufferTimeRestriction &&
                    (!isFirstStationIn12hrBuffer && isFirstStaion) &&
                    (startTime == null || startTime == "NA")) {
                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
                    content: Text(
                        "The time between the current time and the train's arrival time is not within the 2-hour buffer.\n"
                        "If you're unable to see the arrival time, please contact the admin or add the arrival time for this train."),
                    backgroundColor: Color.fromARGB(255, 237, 162, 76),
                    duration: Duration(seconds: 2),
                  ));
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                          "Train Location is not fetched yet, please try again later"),
                      backgroundColor: const Color.fromARGB(255, 237, 162, 76),
                      duration: const Duration(seconds: 2),
                    ),
                  );
                }
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    stationCode,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isOnboardingStation
                          ? Colors.green.shade700
                          : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    stationName.length > 10
                        ? stationName.substring(0, 11)
                        : stationName,
                    style: TextStyle(
                      fontSize: 12,
                      color: isOnboardingStation
                          ? Colors.green.shade600
                          : Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        GestureDetector(
          onTap: () {
            if (totalOnboard == 0 && totalOffboard == 0 && totalVacant == 0) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Chart has not been prepared for this station'),
                  backgroundColor: Colors.orange,
                ),
              );
            } else {
              showDialog(
                context: context,
                builder: (BuildContext context) => StationDetailsPopup(
                  stationCode: stationCode,
                  userName: userName,
                  token: token,
                  queryDate: selectedDate,
                  trainNo: selectedTrain,
                  onboardingDetails: onboardingDetails,
                  offboardingDetails: offboardingDetails,
                  vacantDetails: vacantDetails,
                  coachTotals: coachTotals,
                  filteredCoachTotals: filteredCoachTotals,
                  coachNumbers: coachNumbers,
                  onboardingDetailsInroute: onboardingDetailsInroute,
                  offboardingDetailsInroute: offboardingDetailsInroute,
                  onboardingDetailsCancelled: onboardingDetailsCancelled,
                  offboardingDetailsCancelled: offboardingDetailsCancelled,
                ),
              );
            }
          },
          child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.transparent),
              ),
              child: Column(children: [
                Table(
                  defaultColumnWidth: const IntrinsicColumnWidth(),
                  border: TableBorder.all(
                    color: Colors.white,
                    width: 2,
                  ),
                  children: [
                    TableRow(
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                      ),
                      children: [
                        _buildTableCell('Total',
                            isBold: isOnboardingStation ? true : false,
                            backgroundColor: isOnboardingStation
                                ? Colors.blue.shade200
                                : Colors.blue.shade50,
                            isOnboardingStation: isOnboardingStation),
                        _buildTableCell(totalOnboard.toString(),
                            isBold: isOnboardingStation ? true : false,
                            backgroundColor: isOnboardingStation
                                ? Colors.green.shade200
                                : Colors.green.shade50,
                            isOnboardingStation: isOnboardingStation),
                        _buildTableCell(totalOffboard.toString(),
                            isBold: isOnboardingStation ? true : false,
                            backgroundColor: isOnboardingStation
                                ? const Color.fromARGB(255, 237, 162, 76)
                                : const Color.fromARGB(255, 252, 231, 174),
                            isOnboardingStation: isOnboardingStation),
                        _buildTableCell(totalVacant.toString(),
                            isBold: isOnboardingStation ? true : false,
                            backgroundColor: isOnboardingStation
                                ? Colors.grey.shade300
                                : Colors.grey.shade100,
                            isOnboardingStation: isOnboardingStation),
                      ],
                    ),
                    ...filteredEntries.take(3).map((entry) {
                      return TableRow(
                        children: [
                          _buildTableCell(entry.key,
                              backgroundColor: isOnboardingStation
                                  ? Colors.blue.shade200
                                  : Colors.blue.shade50,
                              isOnboardingStation: isOnboardingStation),
                          _buildTableCell(entry.value['onboard'].toString(),
                              backgroundColor: isOnboardingStation
                                  ? Colors.green.shade200
                                  : Colors.green.shade50,
                              isOnboardingStation: isOnboardingStation),
                          _buildTableCell(entry.value['offboard'].toString(),
                              backgroundColor: isOnboardingStation
                                  ? const Color.fromARGB(255, 237, 162, 76)
                                  : const Color.fromARGB(255, 252, 231, 174),
                              isOnboardingStation: isOnboardingStation),
                          _buildTableCell(entry.value['vacant'].toString(),
                              backgroundColor: isOnboardingStation
                                  ? Colors.grey.shade200
                                  : Colors.grey.shade100,
                              isOnboardingStation: isOnboardingStation),
                        ],
                      );
                    }),
                  ],
                ),
                if (filteredEntries.length > 3)
                  Container(
                    padding: const EdgeInsets.only(top: 2),
                    child: Text(
                      'click for more...',
                      style: TextStyle(
                        fontSize: 8,
                        height: 0.5,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
              ])),
        ),
        if (isAttendanceStation)
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AttendanceDetailsPage(
                      trainNumber: selectedTrain,
                      date: selectedDate,
                      stationCode: stationCode),
                ),
              );
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                "A: $attendanceCount",
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ),
          ),
        const SizedBox(width: 20),
      ],
    );
  }

  Widget _buildTableCell(String text,
      {bool isBold = false,
      Color? backgroundColor,
      bool isOnboardingStation = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      color: backgroundColor,
      child: Text(
        text,
        style: TextStyle(
            fontSize: 10,
            fontWeight: isBold ? FontWeight.bold : FontWeight.w500,
            color: isOnboardingStation ? Colors.black : Colors.grey.shade900),
        textAlign: TextAlign.center,
      ),
    );
  }
}

class StationDetailsPopup extends StatefulWidget {
  final String stationCode;
  final String userName;
  final String queryDate;
  final String trainNo;
  final String token;
  final Map<String, List<int>>? onboardingDetails;
  final Map<String, List<int>>? offboardingDetails;
  final Map<String, List<int>>? vacantDetails;
  final Map<String, Map<String, int>>? coachTotals;
  final Map<String, Map<String, int>>? filteredCoachTotals;
  final List<String> coachNumbers;
  final Map<String, List<int>>? offboardingDetailsInroute;
  final Map<String, List<int>>? onboardingDetailsInroute;
  final Map<String, List<int>>? onboardingDetailsCancelled;
  final Map<String, List<int>>? offboardingDetailsCancelled;

  const StationDetailsPopup({
    super.key,
    required this.stationCode,
    required this.userName,
    required this.queryDate,
    required this.trainNo,
    required this.token,
    this.onboardingDetails,
    this.offboardingDetails,
    this.vacantDetails,
    this.coachTotals,
    this.filteredCoachTotals,
    required this.coachNumbers,
    this.offboardingDetailsInroute,
    this.onboardingDetailsInroute,
    this.onboardingDetailsCancelled,
    this.offboardingDetailsCancelled,
  });

  @override
  State<StationDetailsPopup> createState() => _StationDetailsPopupState();
}

class _StationDetailsPopupState extends State<StationDetailsPopup> {
  bool showSleeperCoaches = false;
  bool isLoadingYourCoaches = false;
  bool isLoadingAllCoaches = false;

  // Track which section has detailed data loaded
  bool yourCoachesDetailedLoaded = false;
  bool allCoachesDetailedLoaded = false;

  // Track which view is currently showing (true = detailed, false = concise)
  bool showYourCoachesDetailed = false;
  bool showAllCoachesDetailed = false;

  TrainScheduleResponse? apiResponse;
  TrainData? targetTrain;
  Map<String, List<int>>? apiOnboardingDetails;
  Map<String, List<int>>? apiOffboardingDetails;
  Map<String, List<int>>? apiAvailableDetails;
  Map<String, List<int>>? apiTransitDetails;
  Map<String, Map<String, int>>? apiCoachTotals;
  Map<String, int>? apiCoachOccupancy;

  // Store detailed berth route information
  Map<String, Map<String, Map<String, String>>>? apiBerthRouteDetails;

  bool isSleeperCoach(String coach) {
    return coach.toLowerCase().startsWith('s');
  }

  Map<String, List<int>> filterSleeperCoaches(
      Map<String, List<int>> details, bool wantSleeper) {
    return Map.fromEntries(
      details.entries
          .where((entry) => isSleeperCoach(entry.key) == wantSleeper),
    );
  }

  Map<String, List<int>> _filterDetailsByCoaches(Map<String, List<int>> details,
      List<String> coachNumbers, bool includeSleeper) {
    return Map.fromEntries(
      details.entries.where((entry) =>
          coachNumbers.contains(entry.key) &&
          isSleeperCoach(entry.key) == includeSleeper),
    );
  }

  bool isInrouteBerth(String coach, int berth) {
    final onboardingInroute = widget.onboardingDetailsInroute ?? {};
    final offboardingInroute = widget.offboardingDetailsInroute ?? {};

    return (onboardingInroute.containsKey(coach) &&
            onboardingInroute[coach]!.contains(berth)) ||
        (offboardingInroute.containsKey(coach) &&
            offboardingInroute[coach]!.contains(berth));
  }

  bool _isCancelledBerth(String coach, int berth) {
    final onboardingCancelled = widget.onboardingDetailsCancelled ?? {};
    final offboardingCancelled = widget.offboardingDetailsCancelled ?? {};

    return (onboardingCancelled.containsKey(coach) &&
            onboardingCancelled[coach]!.contains(berth)) ||
        (offboardingCancelled.containsKey(coach) &&
            offboardingCancelled[coach]!.contains(berth));
  }

  // Get berth route information for detailed view
  String _getBerthRouteInfo(String coach, int berth, String section) {
    if (apiBerthRouteDetails == null) return '';

    final coachData = apiBerthRouteDetails![coach];
    if (coachData == null) return '';

    final berthData = coachData[berth.toString()];
    if (berthData == null) return '';

    switch (section.toLowerCase()) {
      case 'onboarding':
        return berthData['occupied'] ?? '';
      case 'offboarding':
        return berthData['occupied'] ?? '';
      case 'vacant':
        return berthData['available'] ?? berthData['vacant'] ?? '';
      default:
        return berthData['occupied'] ?? '';
    }
  }

  // Fetch detailed data from API
  Future<void> _fetchDetailedData(String section) async {
    if ((section == 'your' && isLoadingYourCoaches) ||
        (section == 'all' && isLoadingAllCoaches)) return;

    setState(() {
      if (section == 'your') {
        isLoadingYourCoaches = true;
      } else {
        isLoadingAllCoaches = true;
      }
    });

    try {
      final response = await PassengerChartDetails.getAllDetails(
        userName: widget.userName,
        queryDate: widget.queryDate,
        trainNo: widget.trainNo,
        stationCode: widget.stationCode,
        token: widget.token,
      );

      TrainData? foundTrain;
      for (var train in response.trains) {
        if (train.trainNo.toString() == widget.trainNo) {
          foundTrain = train;
          break;
        }
      }

      if (foundTrain == null) {
        throw Exception('Train ${widget.trainNo} not found in response');
      }

      final extractedData =
          _extractDataFromTrainData(foundTrain, widget.stationCode);

      setState(() {
        apiResponse = response;
        targetTrain = foundTrain;
        apiOnboardingDetails = extractedData['onboarding'];
        apiOffboardingDetails = extractedData['offboarding'];
        apiAvailableDetails = extractedData['available'];
        apiTransitDetails = extractedData['transit'];
        apiCoachTotals = extractedData['coachTotals'];
        apiCoachOccupancy = extractedData['coachOccupancy'];
        apiBerthRouteDetails = extractedData['berthRouteDetails'];

        if (section == 'your') {
          isLoadingYourCoaches = false;
          yourCoachesDetailedLoaded = true;
          showYourCoachesDetailed = true;
        } else {
          isLoadingAllCoaches = false;
          allCoachesDetailedLoaded = true;
          showAllCoachesDetailed = true;
        }
      });
    } catch (e) {
      setState(() {
        if (section == 'your') {
          isLoadingYourCoaches = false;
        } else {
          isLoadingAllCoaches = false;
        }
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load detailed data: $e'),
            backgroundColor: Colors.red,
          ),
        );
        print('Error fetching detailed data: $e');
      }
    }
  }

  // Extract data from TrainData
  Map<String, dynamic> _extractDataFromTrainData(
      TrainData trainData, String stationCode) {
    Map<String, List<int>> onboarding = {};
    Map<String, List<int>> offboarding = {};
    Map<String, List<int>> available = {};
    Map<String, List<int>> transit = {};
    Map<String, Map<String, int>> coachTotals = {};
    Map<String, int> coachOccupancy = {};
    Map<String, Map<String, Map<String, String>>> berthRouteDetails = {};

    trainData.assignedCoaches.forEach((coachCode, coachData) {
      _processCoachData(
          coachCode,
          coachData,
          stationCode,
          onboarding,
          offboarding,
          available,
          transit,
          coachTotals,
          coachOccupancy,
          berthRouteDetails);
    });

    trainData.otherCoaches.forEach((coachCode, coachData) {
      _processCoachData(
          coachCode,
          coachData,
          stationCode,
          onboarding,
          offboarding,
          available,
          transit,
          coachTotals,
          coachOccupancy,
          berthRouteDetails);
    });

    return {
      'onboarding': onboarding,
      'offboarding': offboarding,
      'available': available,
      'transit': transit,
      'coachTotals': coachTotals,
      'coachOccupancy': coachOccupancy,
      'berthRouteDetails': berthRouteDetails,
    };
  }

  void _processCoachData(
    String coachCode,
    CoachData coachData,
    String stationCode,
    Map<String, List<int>> onboarding,
    Map<String, List<int>> offboarding,
    Map<String, List<int>> available,
    Map<String, List<int>> transit,
    Map<String, Map<String, int>> coachTotals,
    Map<String, int> coachOccupancy,
    Map<String, Map<String, Map<String, String>>> berthRouteDetails,
  ) {
    List<int> onboardingBerths = [];
    Map<String, Map<String, String>> coachBerthDetails = {};

    // Process onboarding data and store route details
    coachData.onboarding.forEach((berthNo, berthInfo) {
      int? berth = int.tryParse(berthNo);
      if (berth != null) {
        // Store detailed route information
        coachBerthDetails[berthNo] = {
          'occupied': berthInfo.occupied,
          'vacant': berthInfo.vacant,
        };

        if (berthInfo.occupied.contains(stationCode)) {
          onboardingBerths.add(berth);
        }
      }
    });

    List<int> offboardingBerths = [];
    // Process offboarding data and merge route details
    coachData.offboarding.forEach((berthNo, berthInfo) {
      int? berth = int.tryParse(berthNo);
      if (berth != null) {
        // Merge with existing berth details or create new
        if (coachBerthDetails.containsKey(berthNo)) {
          coachBerthDetails[berthNo]!['offboard_occupied'] = berthInfo.occupied;
          coachBerthDetails[berthNo]!['offboard_vacant'] = berthInfo.vacant;
        } else {
          coachBerthDetails[berthNo] = {
            'occupied': berthInfo.occupied,
            'vacant': berthInfo.vacant,
          };
        }

        if (berthInfo.occupied.contains(stationCode)) {
          offboardingBerths.add(berth);
        }
      }
    });

    List<int> availableBerths = [];
    // Process available data and merge route details
    coachData.available.forEach((berthNo, routeInfo) {
      int? berth = int.tryParse(berthNo);
      if (berth != null) {
        // Merge with existing berth details or create new
        if (coachBerthDetails.containsKey(berthNo)) {
          coachBerthDetails[berthNo]!['available'] = routeInfo;
        } else {
          coachBerthDetails[berthNo] = {
            'available': routeInfo,
            'occupied': '',
            'vacant': '',
          };
        }

        if (routeInfo.startsWith(stationCode) ||
            routeInfo.contains("-$stationCode-")) {
          availableBerths.add(berth);
        }
      }
    });

    // Store the complete berth route details for this coach
    if (coachBerthDetails.isNotEmpty) {
      berthRouteDetails[coachCode] = coachBerthDetails;
    }

    if (onboardingBerths.isNotEmpty) {
      onboarding[coachCode] = onboardingBerths..sort();
    }
    if (offboardingBerths.isNotEmpty) {
      offboarding[coachCode] = offboardingBerths..sort();
    }
    if (availableBerths.isNotEmpty) {
      available[coachCode] = availableBerths..sort();
    }

    List<int> transitBerths = [];

    coachTotals[coachCode] = {
      'onboard': onboardingBerths.length,
      'offboard': offboardingBerths.length,
      'available': availableBerths.length,
      'transit': transitBerths.length,
    };

    int totalOccupied = 0;
    coachData.onboarding.forEach((_, berthInfo) {
      totalOccupied += berthInfo.occupied.length;
    });
    coachData.offboarding.forEach((_, berthInfo) {
      totalOccupied += berthInfo.occupied.length;
    });
    coachOccupancy[coachCode] = totalOccupied;
  }

  // Data retrieval methods
  Map<String, List<int>> _getOnboardingData(String section) {
    if (section == 'your') {
      if (yourCoachesDetailedLoaded &&
          showYourCoachesDetailed &&
          apiOnboardingDetails != null) {
        return _filterDetailsByCoaches(
            apiOnboardingDetails!, widget.coachNumbers, showSleeperCoaches);
      }
      return _filterDetailsByCoaches(widget.onboardingDetails ?? {},
          widget.coachNumbers, showSleeperCoaches);
    } else {
      if (allCoachesDetailedLoaded &&
          showAllCoachesDetailed &&
          apiOnboardingDetails != null) {
        return showSleeperCoaches
            ? filterSleeperCoaches(apiOnboardingDetails!, true)
            : filterSleeperCoaches(apiOnboardingDetails!, false);
      }
      return showSleeperCoaches
          ? filterSleeperCoaches(widget.onboardingDetails ?? {}, true)
          : filterSleeperCoaches(widget.onboardingDetails ?? {}, false);
    }
  }

  Map<String, List<int>> _getOffboardingData(String section) {
    if (section == 'your') {
      if (yourCoachesDetailedLoaded &&
          showYourCoachesDetailed &&
          apiOffboardingDetails != null) {
        return _filterDetailsByCoaches(
            apiOffboardingDetails!, widget.coachNumbers, showSleeperCoaches);
      }
      return _filterDetailsByCoaches(widget.offboardingDetails ?? {},
          widget.coachNumbers, showSleeperCoaches);
    } else {
      if (allCoachesDetailedLoaded &&
          showAllCoachesDetailed &&
          apiOffboardingDetails != null) {
        return showSleeperCoaches
            ? filterSleeperCoaches(apiOffboardingDetails!, true)
            : filterSleeperCoaches(apiOffboardingDetails!, false);
      }
      return showSleeperCoaches
          ? filterSleeperCoaches(widget.offboardingDetails ?? {}, true)
          : filterSleeperCoaches(widget.offboardingDetails ?? {}, false);
    }
  }

  Map<String, List<int>> _getVacantData(String section) {
    if (section == 'your') {
      if (yourCoachesDetailedLoaded &&
          showYourCoachesDetailed &&
          apiAvailableDetails != null) {
        return _filterDetailsByCoaches(
            apiAvailableDetails!, widget.coachNumbers, showSleeperCoaches);
      }
      return _filterDetailsByCoaches(
          widget.vacantDetails ?? {}, widget.coachNumbers, showSleeperCoaches);
    } else {
      if (allCoachesDetailedLoaded &&
          showAllCoachesDetailed &&
          apiAvailableDetails != null) {
        return showSleeperCoaches
            ? filterSleeperCoaches(apiAvailableDetails!, true)
            : filterSleeperCoaches(apiAvailableDetails!, false);
      }
      return showSleeperCoaches
          ? filterSleeperCoaches(widget.vacantDetails ?? {}, true)
          : filterSleeperCoaches(widget.vacantDetails ?? {}, false);
    }
  }

  Map<String, Map<String, int>>? _getCoachTotals(String section) {
    if (section == 'your') {
      if (yourCoachesDetailedLoaded &&
          showYourCoachesDetailed &&
          apiCoachTotals != null) {
        Map<String, Map<String, int>> filteredTotals = {};
        apiCoachTotals!.forEach((coach, totals) {
          if (widget.coachNumbers.contains(coach) &&
              isSleeperCoach(coach) == showSleeperCoaches) {
            filteredTotals[coach] = totals;
          }
        });
        return filteredTotals;
      }
      return widget.filteredCoachTotals;
    } else {
      if (allCoachesDetailedLoaded &&
          showAllCoachesDetailed &&
          apiCoachTotals != null) {
        Map<String, Map<String, int>> filteredTotals = {};
        apiCoachTotals!.forEach((coach, totals) {
          if (isSleeperCoach(coach) == showSleeperCoaches) {
            filteredTotals[coach] = totals;
          }
        });
        return filteredTotals;
      }
      return widget.coachTotals;
    }
  }

  Map<String, int> _getCoachOccupancy(String section) {
    if (section == 'your') {
      if (yourCoachesDetailedLoaded &&
          showYourCoachesDetailed &&
          apiCoachOccupancy != null) {
        Map<String, int> filteredOccupancy = {};
        apiCoachOccupancy!.forEach((coach, occupancy) {
          if (widget.coachNumbers.contains(coach) &&
              isSleeperCoach(coach) == showSleeperCoaches) {
            filteredOccupancy[coach] = occupancy;
          }
        });
        return filteredOccupancy;
      }
      // Calculate from existing data if API data not available
      Map<String, int> occupancy = {};
      final coachTotals = _getCoachTotals(section);
      coachTotals?.forEach((coach, totals) {
        occupancy[coach] = (totals['onboard'] ?? 0) + (totals['offboard'] ?? 0);
      });
      return occupancy;
    } else {
      if (allCoachesDetailedLoaded &&
          showAllCoachesDetailed &&
          apiCoachOccupancy != null) {
        Map<String, int> filteredOccupancy = {};
        apiCoachOccupancy!.forEach((coach, occupancy) {
          if (isSleeperCoach(coach) == showSleeperCoaches) {
            filteredOccupancy[coach] = occupancy;
          }
        });
        return filteredOccupancy;
      }
      // Calculate from existing data if API data not available
      Map<String, int> occupancy = {};
      final coachTotals = _getCoachTotals(section);
      coachTotals?.forEach((coach, totals) {
        occupancy[coach] = (totals['onboard'] ?? 0) + (totals['offboard'] ?? 0);
      });
      return occupancy;
    }
  }

  // Check if we're in detailed view mode
  bool _isDetailedView(String section) {
    if (section == 'your') {
      return yourCoachesDetailedLoaded && showYourCoachesDetailed;
    } else {
      return allCoachesDetailedLoaded && showAllCoachesDetailed;
    }
  }

  // Build coach occupancy section
  Widget _buildCoachOccupancySection(String section) {
    final occupancyData = _getCoachOccupancy(section);

    if (occupancyData.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.purpleAccent.shade200,
            Colors.purpleAccent.shade100,
          ],
        ),
        border: Border.all(color: Colors.purpleAccent.shade100),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.purpleAccent.shade400,
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.people,
                color: Colors.purpleAccent,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Coach Occupancy Details',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 12,
            runSpacing: 8,
            children: occupancyData.entries.map((entry) {
              final coach = entry.key;
              final occupancy = entry.value;
              final coachTotals = _getCoachTotals(section);
              final totals = coachTotals?[coach];
              final totalBerths = (totals?['onboard'] ?? 0) +
                  (totals?['offboard'] ?? 0) +
                  (totals?['available'] ?? 0);

              return Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Colors.purpleAccent.shade400),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      coach,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '$occupancy/${totalBerths > 0 ? totalBerths : occupancy}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade800,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  // Build API summary section (only for detailed view)
  Widget _buildApiSummarySection(String section) {
    if (!_isDetailedView(section) || targetTrain == null) {
      return const SizedBox.shrink();
    }

    final onboardingData = _getOnboardingData(section);
    final offboardingData = _getOffboardingData(section);
    final vacantData = _getVacantData(section);

    int totalOnboarding =
        onboardingData.values.fold(0, (sum, berths) => sum + berths.length);
    int totalOffboarding =
        offboardingData.values.fold(0, (sum, berths) => sum + berths.length);
    int totalVacant =
        vacantData.values.fold(0, (sum, berths) => sum + berths.length);
    int totalCoaches = onboardingData.keys
        .toSet()
        .union(offboardingData.keys.toSet())
        .union(vacantData.keys.toSet())
        .length;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.pinkAccent.shade200,
            Colors.pinkAccent.shade100,
          ],
        ),
        border: Border.all(color: Colors.pinkAccent.shade200),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.pinkAccent.shade200,
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              const Icon(
                Icons.analytics,
                color: Colors.pinkAccent,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'API Summary Details',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                    'Total Coaches', '$totalCoaches', Colors.blue),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildSummaryCard(
                    'Onboarding', '$totalOnboarding', Colors.green),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildSummaryCard(
                    'Offboarding', '$totalOffboarding', Colors.orange),
              ),
              const SizedBox(width: 8),
              Expanded(
                child:
                    _buildSummaryCard('Available', '$totalVacant', Colors.grey),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: color.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConciseView(String sectionType) {
    final onboardingData = _getOnboardingData(sectionType);
    final offboardingData = _getOffboardingData(sectionType);
    final vacantData = _getVacantData(sectionType);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Coach Occupancy Section
          _buildCoachOccupancySection(sectionType),

          // Onboarding Section
          if (onboardingData.isNotEmpty) ...[
            _buildConciseSection(
                'Onboarding', onboardingData, Colors.green, sectionType),
            const SizedBox(height: 16),
          ],

          // Offboarding Section
          if (offboardingData.isNotEmpty) ...[
            _buildConciseSection(
                'Offboarding', offboardingData, Colors.orange, sectionType),
            const SizedBox(height: 16),
          ],

          // Vacant Section
          if (vacantData.isNotEmpty) ...[
            _buildConciseSection(
                'Available', vacantData, Colors.blue, sectionType),
            const SizedBox(height: 16),
          ],
        ],
      ),
    );
  }

  Widget _buildConciseSection(String title, Map<String, List<int>> data,
      Color color, String sectionType) {
    final coachTotals = _getCoachTotals(sectionType);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withOpacity(0.8),
            color.withOpacity(0.8),
          ],
        ),
        border: Border.all(color: color.withOpacity(0.5)),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            children: [
              Icon(
                _getSectionIcon(title),
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Text(
                '${data.values.fold(0, (sum, berths) => sum + berths.length)} berths',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Coach details with berths grid inside this card
          ...data.entries.map((entry) {
            final coach = entry.key;
            final berths = entry.value;
            final totals = coachTotals?[coach];

            // Get all berths for this coach across all categories for grid display
            Set<int> allBerths = {};
            final onboardingData = _getOnboardingData(sectionType);
            final offboardingData = _getOffboardingData(sectionType);
            final vacantData = _getVacantData(sectionType);

            allBerths.addAll(onboardingData[coach] ?? []);
            allBerths.addAll(offboardingData[coach] ?? []);
            allBerths.addAll(vacantData[coach] ?? []);

            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                border: Border.all(color: Colors.white.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Coach header with totals
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        coach,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: color.withOpacity(0.8),
                        ),
                      ),
                      if (totals != null)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              'On: ${totals['onboard'] ?? 0}',
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                color: Colors.green.shade600,
                              ),
                            ),
                            Text(
                              'Off: ${totals['offboard'] ?? 0}',
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                color: Colors.orange.shade600,
                              ),
                            ),
                            Text(
                              'Available: ${totals['available'] ?? 0}',
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Berths grid - arranged in rows
                  _buildBerthsGrid(
                      coach,
                      allBerths.toList()..sort(),
                      onboardingData[coach] ?? [],
                      offboardingData[coach] ?? [],
                      vacantData[coach] ?? []),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  // Build berths grid for concise view (remains the same)
  Widget _buildBerthsGrid(
      String coach,
      List<int> allBerths,
      List<int> onboardingBerths,
      List<int> offboardingBerths,
      List<int> vacantBerths) {
    const int berthsPerRow = 6;
    List<Widget> rows = [];
    for (int i = 0; i < allBerths.length; i += berthsPerRow) {
      List<int> rowBerths = allBerths.skip(i).take(berthsPerRow).toList();
      rows.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: rowBerths.map((berth) {
              Color berthColor;
              Color textColor;
              Color borderColor;
              String tooltip;
              String displayText;

              // Check for special states first
              bool isInroute = isInrouteBerth(coach, berth);
              bool isCancelled = _isCancelledBerth(coach, berth);

              if (isInroute) {
                berthColor = Colors.yellow.shade100;
                textColor = Colors.grey.shade600;
                borderColor = Colors.yellow.shade200;
                tooltip = 'In-route (R)';
                displayText = '$berth(R)';
              } else if (isCancelled) {
                berthColor = Colors.red.shade100;
                textColor = Colors.grey.shade600;
                borderColor = Colors.red.shade200;
                tooltip = 'Cancelled (C)';
                displayText = '$berth(C)';
              } else if (onboardingBerths.contains(berth)) {
                berthColor = Colors.green.shade300;
                textColor = Colors.grey.shade600;
                borderColor = Colors.green.shade400;
                tooltip = 'Onboarding';
                displayText = berth.toString();
              } else if (offboardingBerths.contains(berth)) {
                berthColor = Colors.orange.shade300;
                textColor = Colors.grey.shade600;
                borderColor = Colors.orange.shade200;
                tooltip = 'Offboarding';
                displayText = berth.toString();
              } else if (vacantBerths.contains(berth)) {
                berthColor = Colors.grey.shade100;
                textColor = Colors.grey.shade800;
                borderColor = Colors.grey.shade600;
                tooltip = 'Available';
                displayText = berth.toString();
              } else {
                berthColor = Colors.grey.shade200;
                textColor = Colors.grey.shade700;
                borderColor = Colors.grey.shade400;
                tooltip = 'No data';
                displayText = berth.toString();
              }

              return Expanded(
                child: Tooltip(
                  message: '$tooltip - Berth $berth',
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    padding: const EdgeInsets.symmetric(vertical: 6),
                    decoration: BoxDecoration(
                      color: berthColor,
                      border: Border.all(
                        color: borderColor,
                      ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Center(
                      child: Text(
                        displayText,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: textColor,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      );
    }
    return Column(children: rows);
  }

  IconData _getSectionIcon(String title) {
    switch (title.toLowerCase()) {
      case 'onboarding':
        return Icons.login;
      case 'offboarding':
        return Icons.logout;
      case 'available':
        return Icons.event_seat;
      default:
        return Icons.info;
    }
  }

  // Build detailed view (Image 3 style) - List layout with route information
  Widget _buildDetailedView(String sectionType) {
    final onboardingData = _getOnboardingData(sectionType);
    final offboardingData = _getOffboardingData(sectionType);
    final vacantData = _getVacantData(sectionType);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // API Summary Section (only for detailed view)
          _buildApiSummarySection(sectionType),

          // Coach Occupancy Section
          _buildCoachOccupancySection(sectionType),

          // Onboarding Section
          if (onboardingData.isNotEmpty) ...[
            _buildDetailedSection(
                'Onboarding', onboardingData, Colors.green, sectionType),
            const SizedBox(height: 16),
          ],

          // Offboarding Section
          if (offboardingData.isNotEmpty) ...[
            _buildDetailedSection(
                'Offboarding', offboardingData, Colors.orange, sectionType),
            const SizedBox(height: 16),
          ],

          // Vacant Section
          if (vacantData.isNotEmpty) ...[
            _buildDetailedSection(
                'Available', vacantData, Colors.purpleAccent, sectionType),
            const SizedBox(height: 16),
          ],
        ],
      ),
    );
  }

  // Build detailed section for detailed view
  Widget _buildDetailedSection(String title, Map<String, List<int>> data,
      Color color, String sectionType) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withOpacity(0.5),
            color.withOpacity(0.5),
          ],
        ),
        border: Border.all(color: color.withOpacity(0.5)),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.5).withOpacity(0.5),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            children: [
              Icon(
                _getSectionIcon(title),
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Text(
                '${data.values.fold(0, (sum, berths) => sum + berths.length)} berths',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Coach details
          ...data.entries.map((entry) {
            final coach = entry.key;
            final berths = entry.value;

            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: color.withOpacity(0.2)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Coach header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        coach,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: color,
                        ),
                      ),
                      Text(
                        '${berths.length} berths',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: color,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Berths with route information
                  ...berths.map((berth) {
                    String routeInfo = _getBerthRouteInfo(coach, berth, title);
                    bool isInroute = isInrouteBerth(coach, berth);
                    bool isCancelled = _isCancelledBerth(coach, berth);

                    return Container(
                      margin: const EdgeInsets.only(bottom: 4),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: isInroute
                            ? Colors.yellow.shade100
                            : isCancelled
                                ? Colors.red.shade100
                                : Colors.green.shade100,
                        border: Border.all(
                          color: isInroute
                              ? Colors.yellow.shade300
                              : isCancelled
                                  ? Colors.red.shade300
                                  : Colors.green.shade300,
                        ),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        children: [
                          // Berth number
                          Container(
                            width: 40,
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            decoration: BoxDecoration(
                              color: isInroute
                                  ? Colors.yellow.shade300
                                  : isCancelled
                                      ? Colors.red.shade300
                                      : Colors.green.shade200,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Center(
                              child: Text(
                                berth.toString(),
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: isInroute
                                      ? Colors.grey.shade600
                                      : isCancelled
                                          ? Colors.grey.shade600
                                          : Colors.grey.shade600,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),

                          // Route information
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (routeInfo.isNotEmpty)
                                  Text(
                                    routeInfo,
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                if (isInroute || isCancelled)
                                  Text(
                                    isInroute ? 'In-route' : 'Cancelled',
                                    style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.grey.shade600
                                        // : Colors.red.shade600,
                                        ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.95,
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade200,
              spreadRadius: 2,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade300,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.train,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Station Details - ${widget.stationCode}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          'Train ${widget.trainNo} - ${widget.queryDate}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w200,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),

            // Tab Bar
            Container(
              color: Colors.white,
              child: DefaultTabController(
                length: 2,
                child: Column(
                  children: [
                    TabBar(
                      labelColor: Colors.blue.shade500,
                      unselectedLabelColor: Colors.grey.shade400,
                      indicatorColor: Colors.blue.shade500,
                      tabs: const [
                        Tab(text: 'Your Coaches'),
                        Tab(text: 'All Coaches'),
                      ],
                    ),

                    // Controls
                    Container(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          // Coach type toggle
                          Row(
                            children: [
                              Text(
                                'Coach Type:',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.blue.shade600,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Switch(
                                value: showSleeperCoaches,
                                onChanged: (value) {
                                  setState(() {
                                    showSleeperCoaches = value;
                                  });
                                },
                                activeColor: Colors.green.shade600,
                              ),
                              Text(
                                showSleeperCoaches ? 'Sleeper' : 'Non-Sleeper',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.blue.shade600,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Content
            Expanded(
              child: DefaultTabController(
                length: 2,
                child: TabBarView(
                  children: [
                    // Your Coaches Tab
                    _buildCoachSection('your'),

                    // All Coaches Tab
                    _buildCoachSection('all'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCoachSection(String section) {
    final isDetailed = _isDetailedView(section);
    final isLoading =
        section == 'your' ? isLoadingYourCoaches : isLoadingAllCoaches;
    final detailedLoaded = section == 'your'
        ? yourCoachesDetailedLoaded
        : allCoachesDetailedLoaded;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // View controls
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    isDetailed ? Icons.description : Icons.find_in_page,
                    color: Colors.green.shade300,
                    size: 20,
                  ),
                  const SizedBox(width: 3),
                  Text(
                    isDetailed ? 'Detailed View' : 'Concise View',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade600,
                    ),
                  ),
                ],
              ),
              ElevatedButton.icon(
                onPressed: isLoading
                    ? null
                    : () {
                        if (detailedLoaded && isDetailed) {
                          // Switch to concise view
                          setState(() {
                            if (section == 'your') {
                              showYourCoachesDetailed = false;
                            } else {
                              showAllCoachesDetailed = false;
                            }
                          });
                        } else if (detailedLoaded && !isDetailed) {
                          // Switch to detailed view
                          setState(() {
                            if (section == 'your') {
                              showYourCoachesDetailed = true;
                            } else {
                              showAllCoachesDetailed = true;
                            }
                          });
                        } else {
                          // Fetch detailed data for the first time
                          _fetchDetailedData(section);
                        }
                      },
                label: Text(
                  isLoading
                      ? 'Loading...'
                      : detailedLoaded
                          ? (isDetailed ? 'Concise' : 'Detailed')
                          : 'Detailed',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade400,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Content
          Expanded(
            child: isDetailed
                ? _buildDetailedView(section)
                : _buildConciseView(section),
          ),
        ],
      ),
    );
  }
}
