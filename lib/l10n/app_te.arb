{"@@locale": "te", "appTitle": "రెయిల్ఆప్స్", "@appTitle": {"description": "The title of the application"}, "cancel": "", "confirm": "", "delete": "", "edit": "", "error": "", "home": "", "loading": "", "login": "లోగిన్", "@login": {"description": "Login button text"}, "logout": "", "next": "", "no": "", "ok": "", "password": "", "previous": "", "refresh": "", "retry": "", "save": "", "search": "", "settings": "", "submit": "", "username": "", "welcome": "రెయిల్ఆప్స్కి స్వాగతం", "@welcome": {"description": "Welcome message displayed on home screen"}, "yes": "", "test_welcome": "స్వాగతం", "@test_welcome": {"description": "Welcome message for testing", "context": "test_context"}, "test_login": "లాগిన్", "@test_login": {"description": "Login button text", "context": "button_labels"}, "text_menu": "మెనూ", "@text_menu": {"description": "Text for drawer menu header", "context": "custom_drawer"}, "text_train_tracker": "రైలు ట్రాకర్", "@text_train_tracker": {"description": "Text from text_widgets: Train Tracker", "context": "text_widgets"}, "text_assign_ca": "CA కేటాయించండి", "@text_assign_ca": {"description": "Text from text_widgets: Assign CA", "context": "text_widgets"}, "text_assign_cs": "CS కేటాయించండి", "@text_assign_cs": {"description": "Text from text_widgets: Assign CS", "context": "text_widgets"}, "text_pnr_details": "PNR వివరాలు", "@text_pnr_details": {"description": "Text from text_widgets: PNR Details", "context": "text_widgets"}, "text_passenger_chart": "ప్రయాణికుల చార్ట్", "@text_passenger_chart": {"description": "Text from text_widgets: Passenger Chart", "context": "text_widgets"}, "text_map_screen": "మ్యాప్ స్క్రీన్", "@text_map_screen": {"description": "Text from text_widgets: Map Screen", "context": "text_widgets"}, "text_configuration": "కాన్ఫిగరేషన్", "@text_configuration": {"description": "Text from text_widgets: Configuration", "context": "text_widgets"}, "text_reports": "నివేదికలు", "@text_reports": {"description": "Text from text_widgets: Reports", "context": "text_widgets"}, "text_passenger_feedback": "ప్రయాణికుల అభిప్రాయం", "@text_passenger_feedback": {"description": "Text from text_widgets: Passenger Feedback", "context": "text_widgets"}, "text_rake_deficiency_report_1": "రేక్ లోపం నివేదిక", "@text_rake_deficiency_report_1": {"description": "Text from text_widgets: Rake Deficiency Report", "context": "text_widgets"}, "text_obhs_to_mcc": "OBHS నుండి MCC అప్పగింత", "@text_obhs_to_mcc": {"description": "Text from text_widgets: OBHS to MCC Handover", "context": "text_widgets"}, "text_mcc_to_obhs": "MCC నుండి OBHS అప్పగింత", "@text_mcc_to_obhs": {"description": "Text from text_widgets: MCC to OBHS Handover", "context": "text_widgets"}, "text_upload_data": "డేటా అప్‌లోడ్ చేయండి", "@text_upload_data": {"description": "Text from text_widgets: Upload data", "context": "text_widgets"}, "text_user_management": "వినియోగదారు నిర్వహణ", "@text_user_management": {"description": "Text from text_widgets: User Management", "context": "text_widgets"}, "text_issue_management": "సమస్య నిర్వహణ", "@text_issue_management": {"description": "Text from text_widgets: Issue Management", "context": "text_widgets"}, "text_rail_sathi_qr": "రైల్ సాథి QR", "@text_rail_sathi_qr": {"description": "Text from text_widgets: Rail Sathi Qr", "context": "text_widgets"}, "text_customer_care": "కస్టమర్ కేర్", "@text_customer_care": {"description": "Text from text_widgets: Customer Care", "context": "text_widgets"}}